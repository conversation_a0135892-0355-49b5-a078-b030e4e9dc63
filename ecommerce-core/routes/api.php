<?php

use App\Enums\Permission;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\ClassTypeController;
use App\Http\Controllers\DesignationController;
use App\Http\Controllers\InstructorController;
use App\Http\Controllers\NotificationTemplateController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
| Following GraphQL-first approach:
| - GraphQL handles all data retrieval (GET operations)
| - REST API handles only mutations (POST, PUT, DELETE operations)
|
*/

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
*/

// Public authentication routes
Route::prefix('auth')->name('auth.')->group(function () {
    Route::post('login', [AuthController::class, 'login'])->name('login');
    Route::post('register', [AuthController::class, 'register'])->name('register');
});


/*
|--------------------------------------------------------------------------
| User Management Routes (Mutations Only)
|--------------------------------------------------------------------------
*/


// Authenticated routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::prefix('auth')->name('auth.')->group(function () {
        Route::post('logout', [AuthController::class, 'logout'])->name('logout');
    });

    //User Management Routes
    Route::prefix('users')->name('users.')->group(function () {
        Route::post('/', [UserController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_USER);
        Route::put('{user}', [UserController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_USER);
        Route::delete('{user}', [UserController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_USER);
    });

    //Instructor Management Routes
    Route::prefix('instructors')->name('instructors.')->group(function () {
        Route::post('/', [InstructorController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_INSTRUCTOR);
        Route::put('{instructor}', [InstructorController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_INSTRUCTOR);
        Route::post('reorder', [InstructorController::class, 'reorder'])->name('reorder')->middleware('permission:' . Permission::EDIT_INSTRUCTOR);
        Route::delete('{instructor}', [InstructorController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_INSTRUCTOR);
    });

    //Designation Management Routes
    Route::prefix('designations')->name('designations.')->group(function () {
        Route::post('/', [DesignationController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_DESIGNATION);
        Route::put('{designation}', [DesignationController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_DESIGNATION);
        Route::delete('{designation}', [DesignationController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_DESIGNATION);
    });

    // Branch Management Routes
    Route::prefix('branches')->name('branches.')->group(function () {
        Route::post('/', [BranchController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_BRANCH);
        Route::put('{branch}', [BranchController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_BRANCH);
        Route::delete('{branch}', [BranchController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_BRANCH);
    });

    // Product Management Routes
    Route::prefix('products')->name('products.')->group(function () {
        Route::post('/', [ProductController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_PRODUCT);
        Route::put('{product}', [ProductController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_PRODUCT);
        Route::delete('{product}', [ProductController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_PRODUCT);
    });

    // Category Management Routes
    Route::prefix('categories')->name('categories.')->group(function () {
        Route::post('/', [CategoryController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_CATEGORY);
        Route::put('{category}', [CategoryController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_CATEGORY);
        Route::delete('{category}', [CategoryController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_CATEGORY);
    });

    // Class Type Management Routes
    Route::prefix('class-types')->name('class-types.')->group(function () {
        Route::post('/', [ClassTypeController::class, 'store'])->name('store')->middleware('permission:' . Permission::CREATE_CLASS_TYPE);
        Route::put('{class_type}', [ClassTypeController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_CLASS_TYPE);
        Route::delete('{class_type}', [ClassTypeController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::DELETE_CLASS_TYPE);
    });

    // S3 File Upload Routes
    Route::prefix('s3-uploads')->name('s3-uploads.')->group(function () {
        Route::post('presigned-url', [UploadController::class, 'generatePresignedUrl'])->name('presigned-url');
    });


    // Notification Template Management Routes
    Route::prefix('notification-templates')->name('notification-templates.')->group(function () {
        Route::post('/', [NotificationTemplateController::class, 'store'])->name('store')->middleware('permission:' . Permission::EDIT_NOTIFICATION_TEMPLATE);
        Route::put('{notification_template}', [NotificationTemplateController::class, 'update'])->name('update')->middleware('permission:' . Permission::EDIT_NOTIFICATION_TEMPLATE);
        Route::delete('{notification_template}', [NotificationTemplateController::class, 'destroy'])->name('destroy')->middleware('permission:' . Permission::EDIT_NOTIFICATION_TEMPLATE);
    });
});
