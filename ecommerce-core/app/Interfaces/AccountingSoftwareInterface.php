<?php

namespace App\Interfaces;

interface AccountingSoftwareInterface
{
    /**
     * Set date from for filtering records
     */
    public function setDateFrom(string $date_from): self;

    /**
     * Set date to for filtering records
     */
    public function setDateTo(string $date_to): self;

    /**
     * Set page number for pagination
     */
    public function setPage(int $page): self;

    /**
     * Set page size for pagination
     */
    public function setPageSize(int $size): self;



    /**
     * Get products from accounting software (single page)
     */
    public function getProducts(): array;

    /**
     * Get specific product by ID
     */
    public function getProduct(string $product_id): array;



    /**
     * Check if the adapter is properly configured
     */
    public function isConfigured(): bool;
}
