<?php

namespace App\Repositories;

use App\Models\Product;
use App\Traits\HasMediaManagement;
use App\Models\ProductBundleItem;
use Prettus\Repository\Criteria\RequestCriteria;
use Prettus\Repository\Eloquent\BaseRepository;

class ProductRepository extends BaseRepository
{
    use HasMediaManagement;
    /**
     * Specify Model class name
     *
     * @return string
     */
    public function model(): string
    {
        return Product::class;
    }

    /**
     * Boot up the repository, pushing criteria
     */
    public function boot()
    {
        $this->pushCriteria(app(RequestCriteria::class));
    }

    /**
     * Set a single image for the product
     */
    public function setImage(Product $product, ?string $url = null): static
    {
        return $this->setMedia($product, $url, 'image');
    }

    /**
     * Set gallery images for the product
     */
    public function setGallery(Product $product, array $urls = []): static
    {
        return $this->setMediaCollection($product, $urls, 'gallery');
    }

    /**
     * Delete gallery media items by their URLs
     */
    public function deleteGalleryByUrls(Product $product, array $urls = []): static
    {
        return $this->deleteMediaByUrls($product, $urls, 'gallery');
    }

    /**
     * Delete the single image from the product
     */
    public function deleteImage(Product $product): static
    {
        return $this->clearMediaCollection($product, 'image');
    }

    /**
     * Sync bundle items for a given bundle product.
     * Each item uses (item_product_id) as identifier within a bundle.
     */
    public function syncBundleItems(Product $bundle, array $items): static
    {
        $bundle_id = $bundle->id;

        $existing = ProductBundleItem::query()
            ->where('bundle_product_id', $bundle_id)
            ->get()
            ->keyBy('item_product_id');

        $incoming_ids = [];

        foreach ($items as $row) {
            $item_product_id = (int) ($row['product_id'] ?? $row['item_product_id'] ?? 0);
            if (!$item_product_id) {
                continue;
            }
            $incoming_ids[] = $item_product_id;

            $payload = [
                'bundle_product_id' => $bundle_id,
                'item_product_id' => $item_product_id,
                'quantity' => max(1, (int) ($row['quantity'] ?? 1)),
            ];

            if ($existing->has($item_product_id)) {
                $existing[$item_product_id]->update($payload);
            } else {
                ProductBundleItem::create($payload);
            }
        }

        // Delete items not present anymore
        $to_delete = $existing->keys()->diff($incoming_ids);
        if ($to_delete->isNotEmpty()) {
            ProductBundleItem::query()
                ->where('bundle_product_id', $bundle_id)
                ->whereIn('item_product_id', $to_delete->all())
                ->delete();
        }

        return $this;
    }

    public function syncCategories(Product $product, array $product_category_ids): void
    {
        $product->categories()->sync($product_category_ids);
    }
}
