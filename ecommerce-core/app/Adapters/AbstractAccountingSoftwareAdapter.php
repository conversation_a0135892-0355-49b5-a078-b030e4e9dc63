<?php

namespace App\Adapters;

use App\Interfaces\AccountingSoftwareInterface;

abstract class AbstractAccountingSoftwareAdapter implements AccountingSoftwareInterface
{
    protected string $dateFrom;
    protected string $dateTo;
    protected int $page = 1;
    protected int $limit = 100;
    protected bool $isConfigured = false;

    public function setDateFrom(string $date_from): AccountingSoftwareInterface
    {
        $this->dateFrom = $date_from;
        return $this;
    }

    public function setDateTo(string $date_to): AccountingSoftwareInterface
    {
        $this->dateTo = $date_to;
        return $this;
    }

    public function setPage(int $page): AccountingSoftwareInterface
    {
        $this->page = $page;
        return $this;
    }

    public function setPageSize(int $size): AccountingSoftwareInterface
    {
        $this->limit = $size;
        return $this;
    }

    public function isConfigured(): bool
    {
        return $this->isConfigured;
    }

    /**
     * Abstract method for making API requests
     * Each adapter should implement this based on their API requirements
     */
    abstract protected function makeRequest(string $endpoint, array $data = [], string $method = 'POST'): array;

    /**
     * Abstract method for handling API errors
     * Each adapter should implement this based on their error handling needs
     */
    abstract protected function handleError(array $response): void;
}
