<?php

namespace App\Adapters;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SiteGiantAdapter extends AbstractEcommerceProviderAdapter
{
    protected $baseUrl;
    protected $token;

    public function __construct()
    {
        $this->baseUrl = config('services.sitegiant.base_url');
        $this->token = $this->getToken();
        $this->page = 1;
        $this->limit = 100;
        $this->channelId = null;
        $this->orderStatuses = [];
    }

    public function getOrders(): array
    {
        $data = [
            'page' => $this->page,
            'limit' => $this->limit,
            'date_from' => $this->dateFrom->format('Y-m-d'),
            'date_to' => $this->dateTo->format('Y-m-d'),
        ];

        if($this->channelId) {
            $data['channel_id'] = $this->channelId;
        }

        if($this->orderStatuses) {
            $data['order_status'] = implode(',', $this->orderStatuses);
        }

        $response = $this->request('get', 'orders', $data);


        return $response['response'];
    }

    public function getOrder(): array
    {
        $response = $this->request('get', 'orders/' . $this->orderId);

        return $response['response'];
    }

    public function getBranches(): array
    {
        $response = $this->request('get', 'channels');

        return $response['response']['channel_list'];
    }

    public function getPaymentMethods(): array
    {
        $response = $this->request('get', 'paymentMethods');

        return $response['response']['payment_methods'];
    }

    public function getToken()
    {
        $token = cache('sitegiant_token');

        if (!$token) {
            $response = Http::post("{$this->baseUrl}/getAccessToken", [
                'secretKey' => config('services.sitegiant.secret_key'),
                'storeEmail' => config('services.sitegiant.store_email'),
                'partnerToken' => config('services.sitegiant.partner_token'),
            ])->json();

            if ($response['code'] != 200) {
                $this->throwError($response);
            }

            $token = $response['response']['access_token'];

            cache(['sitegiant_token' => $token], now()->addMonth());
        }

        return $token;
    }

    protected function request($method, $endpoint, $data = [])
    {
        $response = Http::contentType('application/json')
            ->withHeader('Access-Token', $this->token)
            ->$method("{$this->baseUrl}/{$endpoint}", $data);

        $response->onError(function ($response) {
            if ($response->getStatusCode() == 403) {
                cache()->delete('sitegiant_token');

                $this->getToken();
            }

            $this->throwError(['code' => $response->getStatusCode(), 'message' => $response->getReasonPhrase()]);
        });

        $response = $response->json();


        if ($response['code'] != 200) {
            $this->throwError($response);
        }

        return $response;
    }

    protected function throwError(array $response): void
    {
        Log::error('Sitegiant API Error', $response);

        abort(500, json_encode(['title' => 'Sitegiant API Error', 'data' => $response]));
    }
}
