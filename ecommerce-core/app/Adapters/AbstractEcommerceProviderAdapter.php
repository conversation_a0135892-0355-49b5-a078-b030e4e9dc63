<?php

namespace App\Adapters;

use App\Interfaces\EcommerceProviderInterface;
use Illuminate\Support\Carbon;

abstract class AbstractEcommerceProviderAdapter implements EcommerceProviderInterface
{
    protected Carbon $dateFrom;
    protected Carbon $dateTo;
    protected int $page;
    protected int $limit;
    protected $channelId;

    protected array $orderStatuses;

    protected string $orderId;

    public function setOrderDateFrom(Carbon $date_from): EcommerceProviderInterface
    {
        $this->dateFrom = $date_from;

        return $this;
    }

    public function setOrderDateTo(Carbon $date_to): EcommerceProviderInterface
    {
        $this->dateTo = $date_to;

        return $this;
    }

    public function setOrderPage(int $page): EcommerceProviderInterface
    {
        $this->page = $page;

        return $this;
    }

    public function setOrderId(string $order_id): EcommerceProviderInterface
    {
        $this->orderId = $order_id;

        return $this;
    }

    public function setOrderPageSize(int $size): EcommerceProviderInterface
    {
        $this->limit = $size;

        return $this;
    }

    public function setChannelId(int $channelId): EcommerceProviderInterface
    {
        $this->channelId = $channelId;

        return $this;
    }

    public function setOrderStatuses(array $statuses): EcommerceProviderInterface
    {
        $this->orderStatuses = $statuses;
        return $this;
    }

}
