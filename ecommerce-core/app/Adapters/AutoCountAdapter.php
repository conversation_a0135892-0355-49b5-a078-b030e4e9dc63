<?php

namespace App\Adapters;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class AutoCountAdapter extends AbstractAccountingSoftwareAdapter
{
    protected string $baseUrl;
    protected string $bookId;
    protected string $apiKey;
    protected string $keyId;

    public function __construct()
    {
        $config = config('services.autocount');

        $this->baseUrl = $config['base_url'] ?? '';
        $this->bookId = $config['book_id'] ?? '';
        $this->apiKey = $config['api_key'] ?? '';
        $this->keyId = $config['key_id'] ?? '';

        if (empty($this->baseUrl) || empty($this->bookId) || empty($this->apiKey) || empty($this->keyId)) {
            Log::warning('AutoCount API configuration is incomplete. AutoCount features will be disabled.', [
                'missing_config' => [
                    'base_url' => empty($this->baseUrl),
                    'book_id' => empty($this->bookId),
                    'api_key' => empty($this->apiKey),
                    'key_id' => empty($this->keyId),
                ]
            ]);
            $this->isConfigured = false;
        } else {
            $this->isConfigured = true;
        }
    }

    protected function makeRequest(string $endpoint, array $data = [], string $method = 'POST'): array
    {
        if (!$this->isConfigured) {
            throw new Exception('AutoCount API is not configured. Please check your environment variables.');
        }

        try {
            $url = $this->baseUrl . '/' . $this->bookId . $endpoint;

            Log::info('AutoCount API Request', [
                'url' => $url,
                'method' => $method,
                'data' => $data
            ]);

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'API-Key' => $this->apiKey,
                'Key-ID' => $this->keyId,
            ])->timeout(30);

            $httpResponse = match (strtoupper($method)) {
                'GET' => $response->get($url, $data),
                'POST' => $response->post($url, $data),
                'PUT' => $response->put($url, $data),
                'DELETE' => $response->delete($url, $data),
                default => throw new Exception("Unsupported HTTP method: {$method}")
            };

            if ($httpResponse->failed()) {
                $this->handleError([
                    'status' => $httpResponse->status(),
                    'response' => $httpResponse->body(),
                    'url' => $url
                ]);
            }

            $responseData = $httpResponse->json();

            Log::info('AutoCount API Response', [
                'url' => $url,
                'status' => $httpResponse->status(),
                'data' => $responseData
            ]);

            return $responseData;
        } catch (Exception $e) {
            Log::error('AutoCount API Exception', [
                'message' => $e->getMessage(),
                'url' => $url ?? 'unknown',
                'endpoint' => $endpoint
            ]);

            throw $e;
        }
    }

    protected function handleError(array $response): void
    {
        Log::error('AutoCount API Error', $response);
        throw new Exception("AutoCount API request failed: {$response['status']} - {$response['response']}");
    }





    public function getProducts(): array
    {
        $data = ['page' => $this->page];

        if (isset($this->limit)) {
            $data['limit'] = $this->limit;
        }

        return $this->makeRequest('/product/listing', $data);
    }

    public function getProduct(string $product_id): array
    {
        return $this->makeRequest("/product/{$product_id}", [], 'GET');
    }
}
