<?php

namespace App\Jobs;

use App\Enums\EcommerceProvider;
use App\Enums\OrderStatus;
use App\Enums\OrderType;
use App\Enums\SiteGiantOrderStatus;
use App\Factories\EcommerceProviderAdapterFactory;
use App\Services\BranchService;
use App\Services\OrderService;
use App\Services\OrderStatusService;
use App\Services\PaymentGatewayService;
use App\Services\PaymentMethodService;
use App\Services\ProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class ProcessSiteGiantOrders implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $orders;

    /**
     * Create a new job instance.
     */
    public function __construct(array $orders)
    {
        $this->orders = $orders;
    }

    /**
     * Execute the job.
     */
    public function handle(
        OrderService          $order_service,
        PaymentMethodService  $payment_method_service,
        PaymentGatewayService $payment_gateway_service,
        OrderStatusService    $order_status_service,
        ProductService        $product_service,
        BranchService         $branch_service,
    ): void
    {
        $payment_methods = $payment_method_service->all()->pluck('id', 'title')->toArray();
        $order_statuses = $order_status_service->all()->pluck('id', 'slug')->toArray();
        $products = $product_service->all()->pluck('id', 'sku')->toArray();
        $branches = $branch_service->all()->pluck('id', 'code')->toArray();

        $adapter = EcommerceProviderAdapterFactory::getAdapterFor(EcommerceProvider::SITE_GIANT);

        $payment_gateway = $payment_gateway_service->findVia('sitegiant', 'code');

        foreach ($this->orders as $order_data) {
            $display_id = 'sitegiant_' . $order_data['order_id'];
            $order = $order_service->findVia($display_id, 'display_id');

            if ($order) {
                $order_service->setModel($order)
                    ->update([
                        'status' => $order_statuses[$this->mapOrderStatus($order_data['order_status'])],
                    ]);

                dump('Order #' . $display_id . ' updated');

                continue;
            }

            $response = $adapter
                ->setOrderId($order_data['order_id'])
                ->getOrder();

            if (!isset($payment_methods[$response['payment_method']])) {
                $payment_method = $payment_method_service->store([
                    'gateway_id' => $payment_gateway->id,
                    'title' => $response['payment_method'],
                    'code' => Str::slug($response['payment_method']),
                ])->getModel();

                $payment_methods[$payment_method->title] = $payment_method->id;
            }

            $product_data = [];

            foreach ($response['products'] as $product) {
                $product_data[] = [
                    'sku' => $product['product_sku'],
                    'product_id' => $products[$product['product_sku']] ?? null,
                    'name' => $product['product_name'],
                    'order_quantity' => $product['quantity_purchased'],
                    'invoiced_quantity' => $product['quantity_purchased'],
                    'unit_price' => $product['price'],
                    'tax' => $product['tax'],
                    'subtotal' => $product['total'],
                ];
            }

            $order_data = [
                'type' => OrderType::SIMPLE,
                'require_shipping' => true,
                'customer_name' => $response['buyer_name'],
                'status' => $order_statuses[$this->mapOrderStatus($response['order_status'])],
                'amount' => $response['total_amount'],
                'sales_tax' => $response['tax'],
                'paid_total' => $response['total_amount'],
                'total' => $response['total_amount'],
                'total_invoiced' => $response['total_amount'],
                'payment_method_id' => $payment_methods[$response['payment_method']],
                'billing_address' => $response['billing_address'],
                'shipping_address' => $response['recipient_address'],
                'delivery_fee' => $response['shipping_fee'],
                'delivery_time' => $response['ship_by_date'],
            ];

            $branch_code = 'sitegiant_' . $response['channel']['channel_id'];

            $order_service
                ->store($order_data)
                ->syncProducts($product_data)
                ->syncBranches([$branches[$branch_code]]);

            dump('Order #' . $display_id . ' created');
        }
    }

    private function mapOrderStatus(string $order_status)
    {
        switch ($order_status) {
            case SiteGiantOrderStatus::PAID:
            case SiteGiantOrderStatus::PROCESSED :
                return OrderStatus::PROCESSING;
            case SiteGiantOrderStatus::READY_TO_SHIP:
                return OrderStatus::PACKED;
            case SiteGiantOrderStatus::SHIPPED:
                return OrderStatus::DELIVERY;
            case SiteGiantOrderStatus::COMPLETED:
                return OrderStatus::COMPLETE;
        }
    }
}
