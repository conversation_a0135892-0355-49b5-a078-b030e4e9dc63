<?php

namespace App\Services;

use App\Enums\Role;
use App\Helpers\ErrorCodeHelper;
use App\Models\User;
use App\Repositories\InstructorRepository;
use Illuminate\Support\Arr;

class InstructorService extends BaseService
{
    public function __construct(
        InstructorRepository  $instructor_repository,
        protected UserService $userService
    )
    {
        $this->repository = $instructor_repository;
    }

    public function setAvatar(string $url = null): static
    {
        if (!$url) {
            return $this;
        }

        $this->repository->setAvatar($this->model, $url);

        return $this;
    }

    public function syncUser(array $user_data): static
    {
        if ($user_id = Arr::get($user_data, 'id')) {
            $user = $this->userService->findVia($user_id);

            if ($user->instructor?->id == $this->model->id) {
                return $this;
            }

            $this->validateUser($user);
        }
        else {
            $user_data['roles'][] = Role::INSTRUCTOR;

            $user_data['roles'] = array_unique($user_data['roles']);

            $user = $this->userService
                ->store($user_data)
                ->syncRoles($user_data['roles'])
                ->syncBranches($user_data['branches'])
                ->getModel();
        }

        $this->repository->syncUser($this->model, $user);

        return $this;
    }

    public function syncBranches(array $branches): static
    {
        $this->repository->syncBranches($this->model, $branches);
        return $this;
    }

    public function validateUser(User $user): void
    {
        if ($user->instructor) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::INSTRUCTOR_ERROR, 15001);
        }

        if (!$user->hasRole(Role::INSTRUCTOR)) {
            ErrorCodeHelper::throwError(ErrorCodeHelper::INSTRUCTOR_ERROR, 15002);
        }
    }

    public function reorder(array $instructor_data): static
    {
        foreach ($instructor_data as $data) {
            $this->repository->find($data['id'])->update([
                'order_column' => $data['order_column'],
            ]);
        }

        return $this;
    }
}
