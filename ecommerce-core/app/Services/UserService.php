<?php

namespace App\Services;

use App\Repositories\UserProfileRepository;
use App\Repositories\UserRepository;

class UserService extends BaseService
{
    public function __construct(
        UserRepository                  $user_repository,
        protected NotificationService   $notificationService,
        protected UserProfileRepository $userProfileRepository
    )
    {
        $this->repository = $user_repository;
        $this->relationship = ['profile', 'branches'];
    }

    public function syncProfile(array $profile_data): static
    {
        $this->userProfileRepository->updateOrCreate(
            ['customer_id' => $this->model->id],
            $profile_data
        );

        return $this;
    }

    public function syncRoles(array $roles): static
    {
        $this->repository->syncRoles($this->model, $roles);
        return $this;
    }

    public function syncBranches(array $branches): static
    {
        $this->repository->syncBranches($this->model, $branches);
        return $this;
    }

    public function sendWelcomeNotification(): static
    {
        $data = [
            'user_name' => $this->model->name,
            'phone_number' => $this->model->phone,
            'email' => $this->model->email,
        ];

        $this->notificationService
            ->setUser($this->model)
            ->setTemplateName('welcome_notification')
            ->setTemplateData($data)
            ->send();

        return $this;
    }
}
