<?php

namespace App\Services;

use App\Repositories\OrderRepository;


class OrderService extends BaseService
{
    public function __construct(
        OrderRepository               $orderRepository,
        protected OrderProductService $orderProductService
    )
    {
        $this->repository = $orderRepository;
    }

    public function syncProducts(array $product_data): static
    {
        $this->repository->clearProducts($this->model);

        foreach ($product_data as $product) {
            $product['order_id'] = $this->model->id;
            $this->orderProductService->store($product);
        }

        return $this;
    }

    public function syncBranches(array $branches): static
    {
        $this->repository->syncBranches($this->model, $branches);
        return $this;
    }
}
