<?php

namespace App\Services;

use App\Repositories\ProductRepository;
use Illuminate\Support\Facades\Log;

class ProductService extends BaseService
{
    public function __construct(
        ProductRepository               $product_repository,
        protected ProductVariantService $productVariantService
    ) {
        $this->repository = $product_repository;
    }

    /**
     * Sync product variants - handles create, update, and delete operations
     *
     * This method will:
     * 1. Create new variants (those without ID)
     * 2. Update existing variants (those with ID)
     * 3. Delete variants that are no longer in the provided array
     */
    public function syncVariants(array $variants): static
    {
        // Get all existing variant IDs for this product
        $existing_variant_ids = $this->model->variants()->pluck('id')->toArray();

        // If no variants provided, delete all existing variants
        if (empty($variants)) {
            if (!empty($existing_variant_ids)) {
                $this->model->variants()->delete();

                Log::info('Deleted all product variants', [
                    'product_id' => $this->model->id,
                    'deleted_variant_ids' => $existing_variant_ids
                ]);
            }
            return $this;
        }

        // Track which existing variants are being kept/updated
        $variant_ids_to_keep = [];

        // Process each variant in the input array
        foreach ($variants as $variant_data) {
            $gallery_urls = $variant_data['gallery'] ?? [];

            // Remove gallery from variant data since it's not fillable
            unset($variant_data['gallery']);

            if (isset($variant_data['id']) && !empty($variant_data['id'])) {
                // Update existing variant
                $variant_ids_to_keep[] = $variant_data['id'];

                $this->productVariantService
                    ->setModelById($variant_data['id'])
                    ->update($variant_data)
                    ->setGallery($gallery_urls);
            } else {
                // Create new variant
                $variant_data['product_id'] = $this->model->id;

                $new_variant = $this->productVariantService
                    ->store($variant_data)
                    ->setGallery($gallery_urls)
                    ->getModel();

                // Add the new variant ID to the keep list
                $variant_ids_to_keep[] = $new_variant->id;
            }
        }

        // Delete variants that are no longer needed
        $variant_ids_to_delete = array_diff($existing_variant_ids, $variant_ids_to_keep);

        if (!empty($variant_ids_to_delete)) {
            $this->model->variants()->whereIn('id', $variant_ids_to_delete)->delete();

            Log::info('Deleted product variants', [
                'product_id' => $this->model->id,
                'deleted_variant_ids' => $variant_ids_to_delete,
                'kept_variant_ids' => $variant_ids_to_keep
            ]);
        }

        return $this;
    }

    public function setImage(?string $url = null): static
    {
        if (!$url) {
            return $this;
        }

        $this->repository->setImage($this->model, $url);

        return $this;
    }

    public function setGallery(array $urls = []): static
    {
        if (empty($urls)) {
            return $this;
        }

        $this->repository->setGallery($this->model, $urls);

        return $this;
    }


    /**
     * Delete gallery items by URL list
     */
    public function deleteGallery(array $urls = []): static
    {
        if (empty($urls)) {
            return $this;
        }
        $this->repository->deleteGalleryByUrls($this->model, $urls);
        return $this;
    }

    /**
     * Delete product single image
     */
    public function deleteImage(): static
    {
        $this->repository->deleteImage($this->model);
        return $this;
    }


    /**
     * Store a bundle product (sets is_bundle flag)
     */
    public function storeBundle(array $data): static
    {
        $data['is_bundle'] = true;
        return $this->store($data);
    }

    /**
     * Sync bundle items - handles create, update, and delete operations
     */
    public function syncBundleItems(array $items): static
    {
        $this->repository->syncBundleItems($this->model, $items);
        return $this;
    }


    /**
     * Get model with relationships loaded
     */
    public function getModel()
    {
        $this->model->loadMissing(['variants', 'type']);
        return parent::getModel();
    }
}
