<?php

namespace App\Services;

use App\Repositories\ClassTypeRepository;

class ClassTypeService extends BaseService
{
    public function __construct(ClassTypeRepository $class_type_repository)
    {
        $this->repository = $class_type_repository;
    }

    public function syncCategories(array $category_ids): static
    {
        // Sync categories to the associated product, not directly to ClassType
        if ($this->model->product) {
            $this->model->product->categories()->sync($category_ids);
        }

        return $this;
    }
}
