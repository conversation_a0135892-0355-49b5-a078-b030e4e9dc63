<?php

namespace App\Services;

use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\ClassType;
use App\Repositories\ClassTypeRepository;

class ClassTypeService extends BaseService
{
    public function __construct(
        ClassTypeRepository      $class_type_repository,
        protected ProductService $productService
    )
    {
        $this->repository = $class_type_repository;
    }

    public function createProduct(array $product_category_ids = []): static
    {
        $product_data = [
            'productable_id' => $this->model->id,
            'productable_type' => ClassType::class,
            'type' => ProductType::CLASS_TYPE,
            'name' => $this->model->name,
            'description' => $this->model->description,
            'price' => $this->model->price,
            'tax_class_id' => $this->model->tax_class_id,
            'status' => $this->model->is_active ? ProductStatus::PUBLISH : ProductStatus::INACTIVE,
        ];

        $product_service = $this->productService->store($product_data);

        if ($product_category_ids) {
            $product_service->syncCategories($product_category_ids);
        }

        return $this;
    }

    public function updateProduct(array $product_category_ids = []): static
    {
        if (!$this->model->product) {
            return $this;
        }

        $product_data = [
            'productable_id' => $this->model->id,
            'productable_type' => ClassType::class,
            'type' => ProductType::CLASS_TYPE,
            'name' => $this->model->name,
            'description' => $this->model->description,
            'price' => $this->model->price,
            'tax_class_id' => $this->model->tax_class_id,
            'status' => $this->model->is_active ? ProductStatus::PUBLISH : ProductStatus::INACTIVE,
        ];

        $product_service = $this->productService
            ->setModel($this->model->product)
            ->update($product_data);

        if ($product_category_ids) {
            $product_service->syncCategories($product_category_ids);
        }

        return $this;
    }

    public function deleteProduct(): static
    {
        if (!$this->model->product) {
            return $this;
        }

        $this->productService
            ->setModel($this->model->product)
            ->delete();

        return $this;
    }
}
