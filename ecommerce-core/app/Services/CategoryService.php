<?php

namespace App\Services;

use App\Helpers\ErrorCodeHelper;
use App\Repositories\CategoryRepository;

class CategoryService extends BaseService
{
    public function __construct(CategoryRepository $category_repository)
    {
        $this->repository = $category_repository;
    }

    public function setImage(string $url = null): static
    {
        if (!$url) {
            return $this;
        }

        $this->repository->setImage($this->model, $url);

        return $this;
    }


    /**
     * Delete a category after checking if it has children.
     */
    public function delete(): static
    {
        // Check if category has children
        if ($this->model->hasChildren()) {
            ErrorCodeHelper::throwError(
                ErrorCodeHelper::MASTER_DATA_ERROR,
                10001
            );
        }

        return parent::delete();
    }
}
