<?php

namespace App\Services;

use App\Repositories\ProductVariantRepository;

class ProductVariantService extends BaseService
{
    public function __construct(
        ProductVariantRepository $productVariantRepository
    )
    {
        $this->repository = $productVariantRepository;
        $this->relationship = ['product'];
    }

    public function setGallery(array $urls = []): static
    {
        if (empty($urls)) {
            return $this;
        }

        $this->repository->setGallery($this->model, $urls);

        return $this;
    }
}
