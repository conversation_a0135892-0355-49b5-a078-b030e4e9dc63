<?php

namespace App\Http\Controllers;

use App\Http\Requests\Product\CreateProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Http\Resources\ApiResponse;
use App\Models\Product;
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    public function __construct(protected ProductService $productService) {}

    /**
     * Store a newly created product.
     *
     * @param CreateProductRequest $request
     * @return JsonResponse
     */
    public function store(CreateProductRequest $request): JsonResponse
    {
        $input = $request->validated();

        // Use storeBundle if is_bundle is true, otherwise regular store
        $service = $input['is_bundle'] ?? false
            ? $this->productService->storeBundle($input)
            : $this->productService->store($input);

        $product = $service
            ->setImage($input['image'] ?? null)
            ->setGallery($input['gallery'] ?? [])
            ->syncVariants($input['variants'] ?? []);

        // Always sync bundle items - pass empty array if not a bundle
        $bundle_items = ($input['is_bundle'] ?? false) ? ($input['bundle_items'] ?? []) : [];
        $product->syncBundleItems($bundle_items);

        $product = $product->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($product)
            ->getResponse();
    }

    /**
     * Update the specified product.
     *
     * @param UpdateProductRequest $request
     * @param Product $product
     * @return JsonResponse
     */
    public function update(UpdateProductRequest $request, Product $product): JsonResponse
    {
        $input = $request->validated();

        $service = $this->productService->setModel($product);

        // Update all non-media fields
        $service->update($input);

        // Handle media upserts/deletes if provided
        if (!empty($input['upsert']['image'] ?? [])) {
            // Upsert.image is an array; take the last value as current image
            $lastImage = end($input['upsert']['image']);
            if ($lastImage) {
                $service->setImage($lastImage);
            }
        }

        if (!empty($input['delete']['image'] ?? [])) {
            $service->deleteImage();
        }

        if (!empty($input['upsert']['gallery'] ?? [])) {
            $service->setGallery($input['upsert']['gallery']);
        }

        if (!empty($input['delete']['gallery'] ?? [])) {
            $service->deleteGallery($input['delete']['gallery']);
        }

        // Variants sync
        $service->syncVariants($input['variants'] ?? []);

        // Always sync bundle items - pass empty array if not a bundle to clear existing items
        $bundle_items = ($input['is_bundle'] ?? false) ? ($input['bundle_items'] ?? []) : [];
        $service->syncBundleItems($bundle_items);

        $product = $service->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($product)
            ->getResponse();
    }

    /**
     * Remove the specified product.
     *
     * @param Product $product
     * @return JsonResponse
     */
    public function destroy(Product $product): JsonResponse
    {
        $this->productService->setModel($product)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
