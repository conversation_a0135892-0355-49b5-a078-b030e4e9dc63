<?php

namespace App\Http\Controllers;


use App\Http\Requests\User\CreateUserRequest;
use App\Http\Requests\User\UpdateUserRequest;
use App\Http\Resources\ApiResponse;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;

class UserController extends Controller
{
    public function __construct(protected UserService $userService)
    {
    }

    /**
     *
     * @param CreateUserRequest $request
     * @return JsonResponse
     */
    public function store(CreateUserRequest $request): JsonResponse
    {
        $input = $request->validated();
        $user = $this->userService
            ->store($input)
            ->syncProfile($input['profile'] ?? [])
            ->syncRoles($input['roles'] ?? [])
            ->syncBranches($input['branches'] ?? [])
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setCode(200)
            ->setData($user)
            ->getResponse();
    }

    /**
     *
     * @param UpdateUserRequest $request
     * @param User $user
     * @return JsonResponse
     */
    public function update(UpdateUserRequest $request, User $user): JsonResponse
    {
        $input = $request->validated();
        $user = $this->userService
            ->setModel($user)
            ->update($input)
            ->syncProfile($input['profile'] ?? [])
            ->syncRoles($input['roles'] ?? [])
            ->syncBranches($input['branches'] ?? [])
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($user)
            ->getResponse();
    }

    /**
     *
     * @param User $user
     * @return JsonResponse
     */
    public function destroy(User $user): JsonResponse
    {
        $this->userService->setModel($user)->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
