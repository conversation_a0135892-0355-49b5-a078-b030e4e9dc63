<?php

namespace App\Http\Controllers;

use App\Http\Requests\ClassType\CreateClassTypeRequest;
use App\Http\Requests\ClassType\UpdateClassTypeRequest;
use App\Http\Resources\ApiResponse;
use App\Models\ClassType;
use App\Services\ClassTypeService;
use App\Services\ProductService;
use Illuminate\Http\JsonResponse;

class ClassTypeController extends Controller
{
    public function __construct(
        protected ClassTypeService $classTypeService,
        protected ProductService   $productService
    )
    {
    }

    /**
     * Store a newly created class type.
     *
     * @param CreateClassTypeRequest $request
     * @return JsonResponse
     */
    public function store(CreateClassTypeRequest $request): JsonResponse
    {
        $input = $request->validated();

        // Create ClassType first
        $class_type = $this->classTypeService
            ->store($input)
            ->createProduct($input['category_ids'] ?? [])
            ->getModel();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($class_type)
            ->getResponse();
    }


    /**
     * Update the specified class type.
     *
     * @param UpdateClassTypeRequest $request
     * @param ClassType $class_type
     * @return JsonResponse
     */
    public function update(UpdateClassTypeRequest $request, ClassType $class_type): JsonResponse
    {
        $input = $request->validated();

        // Update ClassType
        $class_type = $this->classTypeService
            ->setModel($class_type)
            ->update($input)
            ->updateProduct($input['category_ids'] ?? [])
            ->getModel();


        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->setData($class_type)
            ->getResponse();
    }

    /**
     * Remove the specified class type.
     *
     * @param ClassType $class_type
     * @return JsonResponse
     */
    public function destroy(ClassType $class_type): JsonResponse
    {
        $this->classTypeService
            ->setModel($class_type)
            ->deleteProduct()
            ->delete();

        return (new ApiResponse())
            ->setMessage(__('api.common.success'))
            ->getResponse();
    }
}
