<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;

class CreateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required|exists:App\Database\Models\Branch,id',
            'customer' => 'required|array',
            'customer.id' => 'nullable',
            'customer.email' => 'nullable|email',
            'customer.phone' => 'nullable|string',
            'billing_address' => 'array',
            'shipping_address' => 'array',
            'state_id' => 'nullable|exists:App\Database\Models\CountryState,id',
            'products' => 'required|array',
            'products.*.id' => 'required|exists:App\Database\Models\Product,id',
            'products.*.product_variant_id' => 'nullable',
            'products.*.quantity' => 'numeric',
            'products.*.price' => 'numeric',
            'shipping_method' => 'string',
            'delivery_fee' => 'numeric',
            'delivery_remarks' => 'nullable|string',
            'discount' => 'numeric',
            'payment_method' => 'sometimes|exists:App\Database\Models\PaymentMethod,id',
            'payment_reference_id' => 'nullable|string',
            'payment_remarks' => 'nullable|string',
            'paid_total' => 'nullable|numeric',
            'comment' => 'nullable|string',
            'coupon' => 'nullable|string',
            'sale_by_id' => ['sometimes', 'exists:users,id'],
            'shop_id' => ['sometimes', 'exists:App\Database\Models\Shop,id'],
        ];
    }
}
