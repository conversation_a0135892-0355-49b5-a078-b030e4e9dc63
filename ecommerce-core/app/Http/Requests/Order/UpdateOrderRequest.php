<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'customer_name' => 'nullable|string',
            'customer_contact' => 'nullable|string',
            'customer_email' => 'nullable|email',
            'status' => 'nullable|exists:App\Models\OrderStatus,id',
            'amount' => 'nullable|numeric',
            'total' => 'nullable|numeric',
            'billing_address' => 'nullable|array',
            'shipping_address' => 'nullable|array',
            'comment' => 'nullable|string',
        ];
    }
}
