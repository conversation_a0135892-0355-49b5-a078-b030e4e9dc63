<?php

namespace App\Models;

use App\Enums\ProductType;
use App\Traits\Productable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClassType extends Model
{
    use HasFactory, SoftDeletes, Productable;

    public function productType(): string
    {
        return ProductType::CLASS_TYPE;
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [
        'id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'decimal:2',
        'duration_in_minutes' => 'integer',
        'class_count' => 'integer',
        'is_addon' => 'boolean',
        'is_bookable' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function taxClass(): BelongsTo
    {
        return $this->belongsTo(TaxClass::class, 'tax_class_id');
    }
}
