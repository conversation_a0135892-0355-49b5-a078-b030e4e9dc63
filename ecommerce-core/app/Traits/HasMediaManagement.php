<?php

namespace App\Traits;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use <PERSON>tie\MediaLibrary\InteractsWithMedia;

trait HasMediaManagement
{
    /**
     * Set a single media item for the model
     * @param HasMedia&InteractsWithMedia $model
     */
    public function setMedia($model, ?string $url = null, string $collection = 'default'): static
    {
        if (!$url) {
            return $this;
        }

        $model->addMediaFromUrl($url)
            ->toMediaCollection($collection);

        return $this;
    }

    /**
     * Set multiple media items for the model
     * @param HasMedia&InteractsWithMedia $model
     */
    public function setMediaCollection($model, array $urls = [], string $collection = 'default'): static
    {
        if (empty($urls)) {
            return $this;
        }

        // Add new media items
        foreach ($urls as $url) {
            if (!empty($url)) {
                $model->addMediaFromUrl($url)
                    ->toMediaCollection($collection);
            }
        }

        return $this;
    }

    /**
     * Delete media items by their URLs from a specific collection.
     * @param HasMedia&InteractsWithMedia $model
     */
    public function deleteMediaByUrls($model, array $urls = [], string $collection = 'default'): static
    {
        if (empty($urls)) {
            return $this;
        }

        // Normalize inputs
        $targets = array_values(array_filter($urls));

        // Only enable basename matching if all targets look like plain filenames (no '/')
        $allBasenames = array_reduce($targets, function ($carry, $t) {
            return $carry && (strpos((string) $t, '/') === false);
        }, true);
        $targetNames = [];
        if ($allBasenames) {
            $targetNames = array_map(function ($u) {
                $path = parse_url($u, PHP_URL_PATH);
                return $path ? basename($path) : basename((string) $u);
            }, $targets);
        }

        $mediaItems = $model->getMedia($collection);
        foreach ($mediaItems as $media) {
            $mediaUrl = $media->getUrl();
            $fullUrl = method_exists($media, 'getFullUrl') ? $media->getFullUrl() : $mediaUrl;
            $fileName = $media->file_name;

            $match = in_array($mediaUrl, $targets, true) || in_array($fullUrl, $targets, true);
            if (!$match && $allBasenames) {
                $match = in_array($fileName, $targetNames, true);
            }

            if ($match) {
                $media->delete();
            }
        }

        return $this;
    }





    /**
     * Clear all media from a specific collection
     * @param HasMedia&InteractsWithMedia $model
     */
    public function clearMediaCollection($model, string $collection): static
    {
        $model->clearMediaCollection($collection);
        return $this;
    }
}
