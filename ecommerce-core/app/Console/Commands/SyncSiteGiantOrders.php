<?php

namespace App\Console\Commands;

use App\Enums\EcommerceProvider;
use App\Enums\SiteGiantOrderStatus;
use App\Factories\EcommerceProviderAdapterFactory;
use App\Jobs\ProcessSiteGiantOrders;
use Illuminate\Console\Command;

class SyncSiteGiantOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitegiant:sync-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $adapter = EcommerceProviderAdapterFactory::getAdapterFor(EcommerceProvider::SITE_GIANT);

        $page = 1;

        do {
            $response = $adapter
                ->setOrderPage($page)
                ->setOrderPageSize(100)
                ->setOrderDateFrom(now()->subDays(2))
                ->setOrderDateTo(now()->subDays())
                ->setOrderStatuses([
                    SiteGiantOrderStatus::COMPLETED,
                    SiteGiantOrderStatus::READY_TO_PICKUP,
                    SiteGiantOrderStatus::PAID,
                    SiteGiantOrderStatus::PROCESSED,
                    SiteGiantOrderStatus::READY_TO_SHIP,
                    SiteGiantOrderStatus::SHIPPED,
                ])
                ->getOrders();

            ProcessSiteGiantOrders::dispatch($response['order_list']);

            $page++;
        } while ($response['more'] == true);
    }
}
