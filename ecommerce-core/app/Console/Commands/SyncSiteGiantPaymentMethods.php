<?php

namespace App\Console\Commands;

use App\Enums\EcommerceProvider;
use App\Factories\EcommerceProviderAdapterFactory;
use App\Services\PaymentGatewayService;
use App\Services\PaymentMethodService;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class SyncSiteGiantPaymentMethods extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitegiant:sync-payment-methods';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync SiteGiant Payment Methods';


    public function __construct(
        protected PaymentMethodService  $paymentMethodService,
        protected PaymentGatewayService $paymentGatewayService
    )
    {
        parent::__construct();

    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $adapter = EcommerceProviderAdapterFactory::getAdapterFor(EcommerceProvider::SITE_GIANT);
        $payment_methods = $adapter->getPaymentMethods();

        $payment_gateway = $this->paymentGatewayService->findVia('sitegiant', 'code');

        if (!$payment_gateway) {
            $payment_gateway = $this->paymentGatewayService->store([
                'title' => 'SiteGiant',
                'code' => 'sitegiant',
            ])->getModel();
        }

        foreach ($payment_methods as $payment_method) {
            $this->paymentMethodService->updateOrCreate([
                'gateway_id' => $payment_gateway->id,
                'title' => $payment_method,
                'code' => Str::slug($payment_method),
            ]);

            $this->info('Payment Method ' . $payment_method . ' synced.');
        }
    }
}
