<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Enums\AccountingSoftwareProvider;
use App\Enums\ProductStatus;
use App\Factories\AccountingSoftwareAdapterFactory;
use App\Services\ProductService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class SyncAutoCountProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'autocount:sync-products
                            {--dry-run : Preview what would be synced without actually syncing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync products from AutoCount API to local database';

    protected ProductService $productService;

    /**
     * Create a new command instance.
     */
    public function __construct(ProductService $productService)
    {
        parent::__construct();
        $this->productService = $productService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        try {
            return $this->handleSync();
        } catch (Exception $e) {
            $this->error('❌ Exception occurred: ' . $e->getMessage());
            return 1;
        }
    }


    /**
     * Handle actual sync or dry run
     *
     * @return int
     */
    private function handleSync(): int
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made to the database');
        } else {
            $this->info('🔄 Starting AutoCount Product Sync...');
        }

        $this->info("ℹ️  Configuration:");
        $this->line("   Mode: " . ($dryRun ? 'Dry Run' : 'Live Sync'));
        $this->line("   Pagination: Automatic (handled by adapter)");



        $result = $this->syncAllProducts($dryRun);
        if (!$result['success']) {
            $this->error('❌ Sync failed: ' . $result['message']);
            return 1;
        }

        $data = $result['data'];

        $this->info('✅ Sync completed successfully!');
        $this->line('');

        $this->table(['Metric', 'Count'], [
            ['Total Products Processed', $data['total_products']],
            ['Products Synced', $data['synced']],
            ['Products Skipped', $data['skipped']],
            ['Errors', $data['errors']]
        ]);

        if ($data['errors'] > 0 && !empty($data['error_details'])) {
            $this->line('');
            $this->warn('⚠️  Errors encountered:');
            foreach (array_slice($data['error_details'], 0, 5) as $error) {
                $this->line("   • {$error}");
            }

            if (count($data['error_details']) > 5) {
                $remaining = count($data['error_details']) - 5;
                $this->line("   ... and {$remaining} more errors");
            }
        }

        if ($dryRun) {
            $this->line('');
            $this->info('💡 This was a dry run. Use --no-dry-run to perform actual sync.');
        }

        return 0;
    }



    /**
     * Sync all products from AutoCount to local database
     *
     * @param bool $dryRun
     * @return array
     */
    protected function syncAllProducts(bool $dryRun = false): array
    {
        try {
            $totalProducts = 0;
            $syncedCount = 0;
            $skippedCount = 0;
            $errorCount = 0;
            $errors = [];

            Log::info('Starting AutoCount product sync', [
                'dry_run' => $dryRun
            ]);

            // Get the adapter
            $adapter = AccountingSoftwareAdapterFactory::getAdapterFor(
                AccountingSoftwareProvider::AUTO_COUNT
            );

            if (!$dryRun) {
                DB::beginTransaction();
            }

            // Use getProducts() with manual pagination for better control
            $this->info("Starting to process products from AutoCount...");

            $page = 1;
            $maxPerPage = 100;

            do {
                $this->info("Processing page {$page}...");

                // Fetch current page
                $adapter->setPage($page)->setPageSize($maxPerPage);
                $response = $adapter->getProducts();

                $products = $response['data'] ?? [];
                $currentPageCount = count($products);

                $this->info("Found {$currentPageCount} products on page {$page}");

                // Process products from current page
                foreach ($products as $autoCountProduct) {
                    try {
                        $syncResult = $this->syncSingleProduct($autoCountProduct, $dryRun);

                        if ($syncResult['action'] === 'synced' || $syncResult['action'] === 'created' || $syncResult['action'] === 'updated') {
                            $syncedCount++;
                        } elseif ($syncResult['action'] === 'skipped') {
                            $skippedCount++;
                        }

                        $totalProducts++;
                    } catch (Exception $e) {
                        $errorCount++;
                        $productCode = $autoCountProduct['product']['productCode'] ?? 'unknown';
                        $errors[] = "Product {$productCode}: " . $e->getMessage();

                        Log::error('Error syncing product', [
                            'product_code' => $productCode,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                // Check if we have more pages
                $totalCount = $response['totalCount'] ?? 0;
                $hasMorePages = $currentPageCount >= $maxPerPage && (($page * $maxPerPage) < $totalCount);

                if ($hasMorePages) {
                    $page++;
                    $this->info("Completed page " . ($page - 1) . ". Moving to page {$page}...");
                } else {
                    $this->info("Completed all pages. Total pages processed: {$page}");
                    break;
                }
            } while (true);

            if (!$dryRun) {
                DB::commit();
            }

            return [
                'success' => true,
                'message' => 'Product sync completed',
                'data' => [
                    'total_products' => $totalProducts,
                    'synced' => $syncedCount,
                    'skipped' => $skippedCount,
                    'errors' => $errorCount,
                    'error_details' => $errors,
                    'dry_run' => $dryRun
                ]
            ];
        } catch (Exception $e) {
            if (!$dryRun) {
                DB::rollBack();
            }

            Log::error('Product sync failed', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Product sync failed: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Sync a single product from AutoCount
     *
     * @param array $autoCountProduct
     * @param bool $dryRun
     * @return array
     */
    protected function syncSingleProduct(array $autoCountProduct, bool $dryRun = false): array
    {
        $product = $autoCountProduct['product'];
        $productCode = $product['productCode'];
        $productId = $product['productId'] ?? null;

        $productData = $this->mapAutoCountToProduct($product);

        if ($dryRun) {
            // For dry run, check if product exists to determine action
            $existingProduct = null;

            if ($productId) {
                $existingProduct = Product::where('external_product_id', $productId)->first();
            }

            return [
                'action' => $existingProduct ? 'would_update' : 'would_create',
                'product_code' => $productCode,
                'product_id' => $productId,
                'existing_local_id' => $existingProduct?->id,
                'mapped_data' => $productData
            ];
        }

        // Simple: Check external_product_id only. If exist, then update, if not match, then create
        if ($productId) {
            $existingProduct = Product::where('external_product_id', $productId)->first();

            if ($existingProduct) {
                // Product exists, update it
                $localProduct = $this->productService
                    ->setModel($existingProduct)
                    ->update($productData)
                    ->getModel();
                $action = 'updated';
            } else {
                // Product doesn't exist, create new one
                $localProduct = $this->productService
                    ->store($productData)
                    ->getModel();
                $action = 'created';
            }
        } else {
            // No external_product_id, just create new product
            $localProduct = $this->productService
                ->store($productData)
                ->getModel();
            $action = 'created';
        }

        return [
            'action' => $action,
            'product_id' => $localProduct->id,
            'product_code' => $productCode,
            'external_product_id' => $productId
        ];
    }

    /**
     * Map AutoCount product data to local Product model
     *
     * This function maps fields from AutoCount API response to our local Product model.
     * Modify the field mappings below to change how data is synchronized.
     *
     * @param array $autoCountProduct
     * @return array
     */
    protected function mapAutoCountToProduct(array $autoCountProduct): array
    {
        // =================================================================
        // FIELD MAPPING CONFIGURATION
        // =================================================================
        // Modify these mappings to change how AutoCount data maps to your Product model

        return [
            // BASIC PRODUCT INFORMATION
            // -------------------------
            'external_product_id' => $autoCountProduct['productId'] ?? null,
            'name' => $autoCountProduct['productName'] ?? 'Unknown Product',
            'sku' => $autoCountProduct['productCode'] ?? null,
            'barcode' => $autoCountProduct['barCode'] ?: null,


            // PRICING
            // -------
            // Convert price to cents (multiply by 100) if your system uses cents
            'price' => $this->convertPrice($autoCountProduct['price'] ?? 0),

            // PRODUCT SETTINGS
            // ----------------
            'unit' => $autoCountProduct['unit'] ?? 'PCS',
            'weight' => null, // AutoCount doesn't provide weight

            // STATUS & VISIBILITY
            // -------------------
            'status' => $this->mapProductStatus($autoCountProduct['status'] ?? 'I'),




            // AUTOCOUNT METADATA
            // ------------------
            // Store original AutoCount data for reference and debugging
            'meta' => $this->buildProductMeta($autoCountProduct)
        ];
    }

    /**
     * Build product description from AutoCount data
     * Modify this to change how descriptions are formatted
     */
    private function buildProductDescription(array $autoCountProduct): ?string
    {
        $description = $autoCountProduct['furtherDescription'] ?? '';

        // Add additional info if available
        if (!empty($autoCountProduct['productCategoryName'])) {
            $description .= $description ? "\n\n" : '';
            $description .= "Category: " . $autoCountProduct['productCategoryName'];
        }

        return !empty($description) ? $description : null;
    }

    /**
     * Convert price based on your system requirements
     * Modify this if you store prices in cents or different format
     */
    private function convertPrice($price): float
    {

        return (float) $price;
    }

    /**
     * Map AutoCount status to your product status
     * Modify these mappings to match your status values
     */
    private function mapProductStatus(string $autoCountStatus): string
    {
        return match ($autoCountStatus) {
            'A' => ProductStatus::PUBLISH,    // Active
            'I' => ProductStatus::DRAFT,  // Inactive
            'D' => ProductStatus::INACTIVE,     // Discontinued
            default => ProductStatus::DRAFT     // Default to draft for unknown statuses
        };
    }


    /**
     * Build metadata object with AutoCount information
     * Simply store the entire AutoCount product data as JSON
     */
    private function buildProductMeta(array $autoCountProduct): array
    {
        return [
            'source' => 'autocount',
            'last_synced_at' => now()->toISOString(),
            'autocount_data' => $autoCountProduct
        ];
    }
}
