# Order GraphQL Schema
# This file defines the GraphQL schema for order-related queries
# Following the GraphQL-first approach: GraphQL for data retrieval, REST for mutations

extend type Query {
  order(id: ID! @eq): Order
    @guard(with: ["sanctum"])
    @hasPermission(permission: "view-order")
    @find(model: "App\\Models\\Order")

  orders(
    where: _
      @whereConditions(
        columns: [
          "id"
          "display_id"
          "external_order_id"
          "tracking_number"
          "type"
          "customer_id"
          "sale_by_id"
          "customer_name"
          "customer_contact"
          "customer_email"
          "status"
          "amount"
          "total"
          "paid_total"
          "payment_gateway"
          "shop_id"
          "created_at"
          "updated_at"
        ]
      )
    orderBy: _
      @orderBy(
        columns: [
          "id"
          "display_id"
          "customer_name"
          "status"
          "amount"
          "total"
          "created_at"
          "updated_at"
        ]
      )
  ): [Order!]!
    @guard(with: ["sanctum"])
    @hasPermission(permission: "view-order")
    @paginate(defaultCount: 15, maxCount: 100)

  orderStatuses: [OrderStatus!]!
    @guard(with: ["sanctum"])
    @hasPermission(permission: "view-order-status")
    @all(model: "App\\Models\\OrderStatus")
}

type Order {
  id: ID!
  display_id: String
  external_order_id: String
  auth_ref_id: String
  tracking_number: String
  type: String
  require_shipping: Boolean!
  customer_id: ID
  sale_by_id: ID
  customer_name: String
  customer_contact: String
  customer_email: String
  amount: Float!
  sales_tax: Float
  paid_total: Float!
  total: Float!
  total_invoiced: Float
  total_refunded: Float
  coupon_id: ID
  parent_id: ID
  shop_id: ID
  coupon_discount: Float
  discount: Float
  points_earn: Float
  points_used: Float
  points_discount: Float
  golds_earn: Float
  golds_used: Float
  golds_discount: Float
  store_credit_earn: Float
  store_credit: Float
  gift_card_credit: Float
  payment_method_id: ID
  payment_method_info: JSON
  payment_id: String
  payment_gateway: String
  shipping_address: JSON
  billing_address: JSON
  state_id: ID
  shipping_class_id: ID
  items_weight: Float
  delivery_fee: Float
  delivery_time: DateTime
  note: String
  created_at: DateTime!
  updated_at: DateTime!

  # Relationships
  customer: User @belongsTo(relation: "customer")
  sale_by: User @belongsTo(relation: "saleBy")
  parent: Order @belongsTo(relation: "parent")
  children: [Order!]! @hasMany(relation: "children")
  order_products: [OrderProduct!]! @hasMany(relation: "products")
  status: OrderStatus
    @belongsTo(relation: "status", foreignKey: "status", ownerKey: "slug")
}

type OrderProduct {
  id: ID!
  order_id: ID!
  product_id: ID!
  inventory_id: ID
  variation_option_id: ID
  name: String!
  sku: String
  barcode: String
  width: Float
  height: Float
  length: Float
  weight: Float
  image: String
  banner: String
  order_quantity: Int!
  invoiced_quantity: Int
  shipped_quantity: Int
  canceled_quantity: Int
  refunded_quantity: Int
  unit_price: Float!
  upfront_amount: Float
  discount_rate_type: String
  discount_rate: Float
  discount: Float
  store_credit: Float
  gift_card_credit: Float
  subtotal: Float!
  tax: Float
  is_commissioned: Boolean
  is_deposit: Boolean
  created_at: DateTime!
  updated_at: DateTime!

  # Relationships
  order: Order! @belongsTo(relation: "order")
  product: Product @belongsTo(relation: "product")
}

type OrderStatus {
  id: ID!
  name: String!
  slug: String!
  serial: Int!
  color: String
  default: Boolean!
  created_at: DateTime!
  updated_at: DateTime!
}
