<?php

use App\Enums\ClassTypePriceType;
use App\Enums\Permission;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Models\Category;
use App\Models\ClassType;
use App\Models\Product;
use App\Models\TaxClass;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    $this->table = ClassType::class;
    $this->productTable = Product::class;
    $this->routeNamePrefix = 'class-types';

    // Setup test users with permissions
    $this->users = setupApiControllerTest([
        Permission::CREATE_CLASS_TYPE,
        Permission::EDIT_CLASS_TYPE,
        Permission::DELETE_CLASS_TYPE,
    ], [
        Permission::CREATE_CLASS_TYPE,
        Permission::EDIT_CLASS_TYPE,
        Permission::DELETE_CLASS_TYPE,
    ]);

    $this->adminUser = $this->users['admin_user'];
    $this->staffUser = $this->users['staff_user'];
    $this->customerUser = $this->users['customer_user'];
    $this->unauthorizedUser = $this->users['unauthorized_user'];
});

describe('store', function () {
    test('with required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Yoga Flow',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'colour' => '#96CEB4',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'duration_in_minutes' => $data['duration_in_minutes'],
                'price_type' => $data['price_type'],
                'colour' => $data['colour'],
            ]);

        $this->assertDatabaseCount($this->table, 1);
        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'duration_in_minutes' => $data['duration_in_minutes'],
            'price_type' => $data['price_type'],
            'colour' => $data['colour'],
        ]);
    });

    test('with optional fields', function () {
        Sanctum::actingAs($this->adminUser);

        $tax_class = TaxClass::factory()->create();

        $data = [
            'name' => 'Advanced Pilates',
            'description' => 'Advanced pilates class for experienced practitioners',
            'duration_in_minutes' => 75,
            'class_count' => 8,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => 120.50,
            'tax_class_id' => $tax_class->id,
            'colour' => '#FF6B6B',
            'is_addon' => true,
            'is_bookable' => true,
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'description' => $data['description'],
                'duration_in_minutes' => $data['duration_in_minutes'],
                'class_count' => $data['class_count'],
                'price_type' => $data['price_type'],
                'price' => '120.50',
                'tax_class_id' => $tax_class->id,
                'colour' => $data['colour'],
                'is_addon' => $data['is_addon'],
                'is_bookable' => $data['is_bookable'],
                'is_active' => $data['is_active'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'description' => $data['description'],
            'duration_in_minutes' => $data['duration_in_minutes'],
            'class_count' => $data['class_count'],
            'price_type' => $data['price_type'],
            'price' => '120.50',
            'tax_class_id' => $tax_class->id,
            'colour' => $data['colour'],
            'is_addon' => $data['is_addon'],
            'is_bookable' => $data['is_bookable'],
            'is_active' => $data['is_active'],
        ]);
    });

    test('with free class type', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Free Trial Class',
            'duration_in_minutes' => 30,
            'price_type' => ClassTypePriceType::FREE,
            'colour' => '#27AE60',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'price_type' => $data['price_type'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'price_type' => $data['price_type'],
            'price' => null,
        ]);
    });

    test('with default boolean values', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Basic Class',
            'duration_in_minutes' => 45,
            'price_type' => ClassTypePriceType::FIXED,
            'colour' => '#4ECDC4',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
            ]);

        // Verify in database
        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'class_count' => 1,
            'is_addon' => false,
            'is_bookable' => true,
            'is_active' => true,
        ]);
    });

    test('automatically creates product when class type is created', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->productTable, 0);

        $data = [
            'name' => 'Yoga Flow',
            'description' => 'A dynamic yoga sequence',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => "25.00",
            'colour' => '#96CEB4',
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Verify product was created
        $this->assertDatabaseCount($this->productTable, 1);

        // Verify product has correct attributes synced from class type
        $this->assertDatabaseHas($this->productTable, [
            'productable_id' => $response['data']['id'],
            'productable_type' => ClassType::class,
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => $data['price'], // Database stores as decimal, comparison should work
            'type' => ProductType::CLASS_TYPE,
            'status' => ProductStatus::PUBLISH, // is_active = true
        ]);
    });

    test('handles price type validation with enum values', function () {
        Sanctum::actingAs($this->adminUser);

        $valid_price_types = [
            ClassTypePriceType::FIXED,
            ClassTypePriceType::FREE,
            ClassTypePriceType::VARIABLE,
        ];

        foreach ($valid_price_types as $price_type) {
            $data = [
                'name' => "Test Class {$price_type}",
                'duration_in_minutes' => 60,
                'price_type' => $price_type,
                'colour' => '#FF0000',
            ];

            $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

            expect($response)->toHaveSuccessGeneralResponse()
                ->and($response['data']['price_type'])->toBe($price_type);
        }
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
            'duration_in_minutes' => ['The duration in minutes field is required.'],
            'price_type' => ['The price type field is required.'],
            'colour' => ['The colour field is required.'],
        ]);
    });

    test('validates field formats and constraints', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => str_repeat('a', 256), // Too long
            'duration_in_minutes' => 0, // Too small
            'class_count' => 0, // Too small
            'price_type' => 'invalid_type',
            'price' => -10, // Negative
            'tax_class_id' => 999999, // Non-existent
            'colour' => 'invalid-color', // Invalid format
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name may not be greater than 255 characters.'],
            'duration_in_minutes' => ['The duration in minutes must be at least 1.'],
            'class_count' => ['The class count must be at least 1.'],
            'price' => ['The price must be at least 0.'],
            'colour' => ['The colour format is invalid.'],
            'price_type' => ['The selected price type is invalid.'],
            'tax_class_id' => ['The selected tax class id is invalid.'],
        ]);
    });

    test('validates hex color format', function () {
        Sanctum::actingAs($this->adminUser);

        $invalid_colors = [
            'red',
            '#FF',
            '#GGGGGG',
            'FF0000',
            '#ff0000g',
        ];

        foreach ($invalid_colors as $color) {
            $data = [
                'name' => 'Test Class',
                'duration_in_minutes' => 60,
                'price_type' => ClassTypePriceType::FIXED,
                'colour' => $color,
            ];

            $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

            expect($response)->toHaveFailedValidationResponse([
                'colour' => ['The colour format is invalid.'],
            ]);
        }
    });

    test('accepts valid hex colors', function () {
        Sanctum::actingAs($this->adminUser);

        $valid_colors = [
            '#FF0000',
            '#00FF00',
            '#0000FF',
            '#FFFFFF',
            '#000000',
            '#123ABC',
        ];

        foreach ($valid_colors as $color) {
            $data = [
                'name' => "Test Class {$color}",
                'duration_in_minutes' => 60,
                'price_type' => ClassTypePriceType::FIXED,
                'colour' => $color,
            ];

            $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

            expect($response)->toHaveSuccessGeneralResponse()
                ->and($response['data']['colour'])->toBe($color);
        }
    });

    test('syncs tax class id to product', function () {
        Sanctum::actingAs($this->adminUser);

        // Create tax class via factory (this is acceptable as it's test data setup)
        $tax_class = TaxClass::factory()->create();

        // Create class type via API with tax class
        $data = [
            'name' => 'Taxable Class',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '30.00',
            'tax_class_id' => $tax_class->id,
            'colour' => '#FF6B6B',
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $class_type = ClassType::first();

        // Verify tax class is synced to product
        expect($class_type->product->tax_class_id)->toBe($tax_class->id);

        $this->assertDatabaseHas($this->productTable, [
            'productable_id' => $class_type->id,
            'tax_class_id' => $tax_class->id,
        ]);
    });

    test('sets product status based on is_active flag', function () {
        Sanctum::actingAs($this->adminUser);

        // Test active class type via API
        $activeData = [
            'name' => 'Active Class',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '25.00',
            'colour' => '#00FF00',
            'is_active' => true,
        ];

        $activeResponse = $this->postJson(route("{$this->routeNamePrefix}.store"), $activeData)->json();
        expect($activeResponse)->toHaveSuccessGeneralResponse();

        $activeClassType = ClassType::where('name', 'Active Class')->first();
        expect($activeClassType->product->status)->toBe(ProductStatus::PUBLISH);

        // Test inactive class type via API
        $inactiveData = [
            'name' => 'Inactive Class',
            'duration_in_minutes' => 45,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '20.00',
            'colour' => '#FF0000',
            'is_active' => false,
        ];

        $inactiveResponse = $this->postJson(route("{$this->routeNamePrefix}.store"), $inactiveData)->json();
        expect($inactiveResponse)->toHaveSuccessGeneralResponse();

        $inactiveClassType = ClassType::where('name', 'Inactive Class')->first();
        expect($inactiveClassType->product->status)->toBe(ProductStatus::INACTIVE);
    });

    test('can access categories through product relationship', function () {
        Sanctum::actingAs($this->adminUser);

        // Create categories first
        $category1 = Category::factory()->create(['name' => 'Fitness']);
        $category2 = Category::factory()->create(['name' => 'Yoga']);

        // Create class type via API
        $data = [
            'name' => 'Yoga Flow with Categories',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '25.00',
            'colour' => '#96CEB4',
            'category_ids' => [$category1->id, $category2->id],
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();
        expect($response)->toHaveSuccessGeneralResponse();

        $class_type = ClassType::query()->find($response['data']['id']);

        // Verify categories are accessible through product
        expect($class_type->product)->not()->toBeNull()
            ->and($class_type->product->categories)->toHaveCount(2);

        // Verify helper method works
        $categories = $class_type->getCategories();
        expect($categories)->toHaveCount(2);

        $category_names = $categories->pluck('name')->toArray();
        expect($category_names)->toContain('Fitness', 'Yoga');
    });
});

describe('update', function () {
    test('update class type', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create([
            'name' => 'Original Name',
            'price_type' => ClassTypePriceType::FIXED,
            'price' => 50.00,
        ]);

        $data = [
            'name' => 'Updated Name',
            'price' => 75.00,
            'is_active' => true,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $class_type->id),
            $data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'id' => $class_type->id,
                'name' => $data['name'],
                'price' => '75.00',
                'is_active' => $data['is_active'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'id' => $class_type->id,
            'name' => $data['name'],
            'price' => 75.00,
            'is_active' => true,
        ]);
    });


    test('can update from fixed to free', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create([
            'price_type' => ClassTypePriceType::FIXED,
            'price' => 50.00,
        ]);

        $data = [
            'price_type' => ClassTypePriceType::FREE,
            'price' => null,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $class_type->id),
            $data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'price_type' => ClassTypePriceType::FREE,
                'price' => null,
            ]);
    });

    test('handles boolean field updates correctly', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create([
            'is_addon' => false,
            'is_bookable' => false,
            'is_active' => false,
        ]);

        $data = [
            'is_addon' => true,
            'is_bookable' => true,
            'is_active' => true,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $class_type->id),
            $data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray($data);
    });

    test('preserves existing data when partially updating', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create([
            'name' => 'Original Name',
            'description' => 'Original Description',
            'price' => 50.00,
            'is_active' => false,
        ]);

        // Only update name
        $data = ['name' => 'Updated Name'];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $class_type->id),
            $data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => 'Updated Name',
                'description' => 'Original Description',
                'price' => '50.00',
                'is_active' => false,
            ]);
    });

    test('automatically updates product when class type is updated', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create([
            'name' => 'Original Name',
            'description' => 'Original Description',
            'duration_in_minutes' => 60,
            'price_type' => ClassTypePriceType::FIXED,
            'price' => '50.00',
            'colour' => '#96CEB4',
            'is_active' => true,
        ]);

        // Product is automatically created via Productable trait
        $this->assertDatabaseCount($this->productTable, 1);

        $data = [
            'name' => 'Updated Name',
            'description' => 'Updated Description',
            'price' => '75.00',
            'is_active' => false,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $class_type->id),
            $data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Verify product was updated (same product, not new one)
        $this->assertDatabaseCount($this->productTable, 1);

        // Refresh the class type to get updated product relationship
        $class_type->refresh();

        // Verify the product was updated with new attributes
        expect($class_type->product->name)->toBe('Updated Name');
        expect($class_type->product->description)->toBe('Updated Description');
        expect($class_type->product->price)->toBe('75.00');
        expect($class_type->product->status)->toBe(ProductStatus::INACTIVE); // is_active = false

        $this->assertDatabaseHas($this->productTable, [
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => $data['price'],
            'status' => ProductStatus::INACTIVE,
        ]);
    });

    test('validates', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create();

        $data = [
            'name' => '', // Empty when required
            'duration_in_minutes' => -5, // Negative
            'price' => -10, // Negative
            'colour' => 'invalid', // Invalid format
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $class_type->id),
            $data
        )->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
            'duration_in_minutes' => ['The duration in minutes must be at least 1.'],
            'price' => ['The price must be at least 0.'],
            'colour' => ['The colour format is invalid.'],
        ]);
    });

    test('with non-existent', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", 999999),
            ['name' => 'Updated Name']
        )->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});

describe('delete', function () {
    test('delete class type', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create();

        $this->assertDatabaseHas($this->table, ['id' => $class_type->id]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $class_type->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, ['id' => $class_type->id]);
    });

    test('with non-existent class type', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", 999999)
        )->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });

    test('can delete with relationships', function () {
        Sanctum::actingAs($this->adminUser);

        $tax_class = TaxClass::factory()->create();
        $class_type = ClassType::factory()->create([
            'tax_class_id' => $tax_class->id,
        ]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $class_type->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, ['id' => $class_type->id]);
        // Tax class should still exist
        $this->assertDatabaseHas('tax_classes', ['id' => $tax_class->id]);
    });

    test('automatically deletes product when class type is deleted', function () {
        Sanctum::actingAs($this->adminUser);

        $class_type = ClassType::factory()->create();
        $product = Product::factory()->create([
            'productable_id' => $class_type->id,
            'productable_type' => ClassType::class,
        ]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $class_type->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        // Verify both class type and product are soft deleted
        $this->assertSoftDeleted($this->table, ['id' => $class_type->id]);
        $this->assertSoftDeleted($this->productTable, ['id' => $product->id]);
    });
});

test('unauthenticated and unauthorized user cannot access class type endpoints', function () {
    $classType = ClassType::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", $classType->id)],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", $classType->id)],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorization
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});
