<?php

use App\Enums\Permission;
use App\Enums\ProductType;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->table = Category::class;
    $this->routeNamePrefix = 'categories';

    // Setup test users with permissions
    $this->users = setupApiControllerTest([
        Permission::CREATE_CATEGORY,
        Permission::EDIT_CATEGORY,
        Permission::DELETE_CATEGORY,
    ], [
        Permission::CREATE_CATEGORY,
        Permission::EDIT_CATEGORY,
        Permission::DELETE_CATEGORY,
    ]);

    $this->adminUser = $this->users['admin_user'];
    $this->staffUser = $this->users['staff_user'];
    $this->customerUser = $this->users['customer_user'];
    $this->unauthorizedUser = $this->users['unauthorized_user'];
});

test('unauthenticated and unauthorized user cannot access category endpoints', function () {
    $category = Category::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['category' => $category->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['category' => $category->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

describe('store', function () {
    test('create without parent', function () {
        Sanctum::actingAs($this->adminUser);

        $this->assertDatabaseCount($this->table, 0);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
            'meta' => [
                'description' => 'Electronic devices and gadgets',
                'keywords' => ['electronics', 'gadgets'],
            ],
            'order_column' => 1,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'name' => $data['name'],
                'slug' => 'electronics',
                'type' => $data['type'],
                'is_active' => $data['is_active'],
                'meta' => $data['meta'],
                'order_column' => $data['order_column'],
            ]);

        $this->assertDatabaseCount($this->table, 1);
        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'slug' => 'electronics',
            'type' => $data['type'],
            'is_active' => $data['is_active'],
            'order_column' => $data['order_column'],
            'meta->description' => $data['meta']['description'],
            'meta->keywords' => json_encode($data['meta']['keywords']),
        ]);
    });

    test('create with parent', function () {
        Sanctum::actingAs($this->adminUser);

        // Create parent category first
        $parent = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['parent_id'])->toBe($parent->id)
            ->and($response['data']['slug'])->toBe('smartphones');

        $this->assertDatabaseHas($this->table, [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'slug' => 'smartphones',
        ]);
    });

    test('auto-generates unique slug when duplicate name exists', function () {
        Sanctum::actingAs($this->adminUser);

        // Create first category
        Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['slug'])->not()->toBe('electronics')
            ->and($response['data']['slug'])->toStartWith('electronics-');
    });

    test('validates required fields', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

        expect($response)->toHaveFailedValidationResponse([
            'name' => ['The name field is required.'],
            'type' => ['The type field is required.'],
        ]);
    });

    test('validates parent_id exists', function () {
        Sanctum::actingAs($this->adminUser);

        $data = [
            'name' => 'Test Category',
            'parent_id' => 999, // Non-existent parent
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

        expect($response)->toHaveFailedValidationResponse([
            'parent_id' => ['The selected parent category does not exist.'],
        ]);
    });
});

describe('update', function () {
    test('update without parent', function () {
        Sanctum::actingAs($this->adminUser);

        $category = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
            'is_active' => true,
        ]);

        $update_data = [
            'name' => 'Electronics & Technology',
            'type' => ProductType::DEFAULT,
            'is_active' => false,
            'meta' => ['updated' => true],
            'order_column' => 5,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            $update_data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data'])->toMatchArray([
                'id' => $category->id,
                'name' => $update_data['name'],
                'slug' => 'electronics-technology',
                'is_active' => $update_data['is_active'],
                'meta' => $update_data['meta'],
                'order_column' => $update_data['order_column'],
            ]);

        $this->assertDatabaseHas($this->table, [
            'id' => $category->id,
            'name' => 'Electronics & Technology',
            'slug' => 'electronics-technology',
            'is_active' => $update_data['is_active'],
            'order_column' => $update_data['order_column'],
            'meta->updated' => $update_data['meta']['updated'],
        ]);
    });

    test('update with parent', function () {
        Sanctum::actingAs($this->adminUser);

        $parent = Category::factory()->create(['name' => 'Electronics']);
        $category = Category::factory()->create(['name' => 'Smartphones']);

        $update_data = [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            $update_data
        )->json();

        expect($response)->toHaveSuccessGeneralResponse()
            ->and($response['data']['parent_id'])->toBe($parent->id);

        $this->assertDatabaseHas($this->table, [
            'id' => $category->id,
            'parent_id' => $parent->id,
        ]);
    });

    test('prevents category from being its own parent', function () {
        Sanctum::actingAs($this->adminUser);

        $category = Category::factory()->create();

        $update_data = [
            'name' => 'Test Category',
            'parent_id' => $category->id, // Self as parent
            'type' => ProductType::DEFAULT,
        ];

        $response = $this->putJson(
            route("{$this->routeNamePrefix}.update", $category->id),
            $update_data
        )->json();

        expect($response)->toHaveFailedValidationResponse([
            'parent_id' => ['A category cannot be its own parent.'],
        ]);
    });
});

describe('delete', function () {
    test('delete without children', function () {
        Sanctum::actingAs($this->adminUser);

        $category = Category::factory()->create();

        $this->assertDatabaseHas($this->table, ['id' => $category->id]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $category->id)
        )->json();

        expect($response)->toHaveSuccessGeneralResponse();

        $this->assertSoftDeleted($this->table, ['id' => $category->id]);
    });

    test('delete with children', function () {
        Sanctum::actingAs($this->adminUser);

        $parent = Category::factory()->create(['name' => 'Electronics']);
        Category::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", $parent->id)
        )->json();

        expect($response)->toHaveFailedGeneralResponse(10001, 'This data cannot be deleted because it is being used.');

        $this->assertDatabaseHas($this->table, ['id' => $parent->id]);
    });

    test('delete non-existent category', function () {
        Sanctum::actingAs($this->adminUser);

        $response = $this->deleteJson(
            route("{$this->routeNamePrefix}.destroy", 999)
        )->json();

        expect($response)->toHaveModelResourceNotFoundResponse();
    });
});
