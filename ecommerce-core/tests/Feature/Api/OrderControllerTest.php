<?php

use App\Enums\Permission;
use App\Models\Branch;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\Product;
use Carbon\Carbon;
use Laravel\Sanctum\Sanctum;

beforeEach(function () {
    // Set up common roles, permissions, and users
    $users = setupApiControllerTest([
        Permission::VIEW_ORDER,
        Permission::EDIT_ORDER,
        Permission::DELETE_ORDER,
    ], [Permission::VIEW_ORDER]);

    // Create test branch for order-related tests
    $this->branch = Branch::factory()->create([
        'name' => 'Test Branch',
        'active' => true,
    ]);

    // Create test order status
    $this->orderStatus = OrderStatus::factory()->create([
        'name' => 'Pending',
        'slug' => 'pending',
    ]);

    // Create test products
    $this->product1 = Product::factory()->create([
        'name' => 'Test Product 1',
        'price' => 100.00,
    ]);

    $this->product2 = Product::factory()->create([
        'name' => 'Test Product 2',
        'price' => 50.00,
    ]);

    // Assign users to test instance
    $this->adminUser = $users->get('admin_user');
    $this->unauthorizedUser = $users->get('unauthorized_user');
    $this->customerUser = $users->get('customer_user');

    $this->routeNamePrefix = 'orders';
    $this->table = Order::class;

    Carbon::setTestNow('2025-01-01 00:00:00');
});

test('unauthenticated and unauthorized user cannot access order endpoints', function () {
    $order = Order::factory()->create();

    // Test all endpoints without authentication
    $endpoints = [
        ['POST', route("{$this->routeNamePrefix}.store")],
        ['PUT', route("{$this->routeNamePrefix}.update", ['order' => $order->id])],
        ['DELETE', route("{$this->routeNamePrefix}.destroy", ['order' => $order->id])],
    ];

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthenticatedResponse();
    }

    // Test all endpoints without authorized
    Sanctum::actingAs($this->unauthorizedUser);

    foreach ($endpoints as [$method, $url]) {
        $response = $this->json($method, $url)->json();
        expect($response)->toHaveUnauthorizedPermissionResponse();
    }
});

test('create order with customer and products', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'branch_id' => $this->branch->id,
        'customer' => [
            'id' => $this->customerUser->id,
        ],
        'products' => [
            [
                'id' => $this->product1->id,
                'quantity' => 2,
                'price' => 100.00,
            ],
            [
                'id' => $this->product2->id,
                'quantity' => 1,
                'price' => 50.00,
            ],
        ],
        'billing_address' => [
            'street' => '123 Main St',
            'city' => 'Test City',
            'state' => 'Test State',
            'postal_code' => '12345',
            'country' => 'Test Country',
        ],
        'shipping_address' => [
            'street' => '456 Oak Ave',
            'city' => 'Test City',
            'state' => 'Test State',
            'postal_code' => '12345',
            'country' => 'Test Country',
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveKey('id')
        ->and($response['data'])->toHaveKey('customer_id', $this->customerUser->id);

    $this->assertDatabaseCount($this->table, 1);

    $order = Order::query()->find($response['data']['id']);
    expect($order)->not->toBeNull()
        ->and($order->customer_id)->toBe($this->customerUser->id);
});

test('create order validates required fields', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), [])->json();

    expect($response)->toHaveFailedValidationResponse([
        'branch_id' => ['The branch id field is required.'],
        'customer' => ['The customer field is required.'],
        'products' => ['The products field is required.'],
    ]);
});

test('create order validates non-existent branch', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'branch_id' => 9999,
        'customer' => [
            'id' => $this->customerUser->id,
        ],
        'products' => [
            [
                'id' => $this->product1->id,
                'quantity' => 1,
                'price' => 100.00,
            ],
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'branch_id' => ['The selected branch id is invalid.'],
    ]);
});

test('create order validates non-existent product', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'branch_id' => $this->branch->id,
        'customer' => [
            'id' => $this->customerUser->id,
        ],
        'products' => [
            [
                'id' => 9999,
                'quantity' => 1,
                'price' => 100.00,
            ],
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveFailedValidationResponse([
        'products.0.id' => ['The selected products.0.id is invalid.'],
    ]);
});

test('create order with new customer', function () {
    Sanctum::actingAs($this->adminUser);

    $this->assertDatabaseCount($this->table, 0);

    $data = [
        'branch_id' => $this->branch->id,
        'customer' => [
            'email' => '<EMAIL>',
            'phone' => '+60123456789',
        ],
        'products' => [
            [
                'id' => $this->product1->id,
                'quantity' => 1,
                'price' => 100.00,
            ],
        ],
    ];

    $response = $this->postJson(route("{$this->routeNamePrefix}.store"), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toHaveKey('id')
        ->and($response['data'])->toHaveKey('customer_email', '<EMAIL>')
        ->and($response['data'])->toHaveKey('customer_contact', '+60123456789');

    $this->assertDatabaseCount($this->table, 1);
});

test('update order', function () {
    Sanctum::actingAs($this->adminUser);

    $order = Order::factory()->create([
        'customer_id' => $this->customerUser->id,
        'amount' => 100.00,
        'total' => 110.00,
    ]);

    $this->assertDatabaseCount($this->table, 1);

    $data = [
        'customer_name' => 'Updated Customer Name',
        'customer_email' => '<EMAIL>',
        'customer_contact' => '+60123456789',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['order' => $order->id]), $data)->json();

    expect($response)->toHaveSuccessGeneralResponse()
        ->and($response['data'])->toMatchArray([
            'customer_name' => $data['customer_name'],
            'customer_email' => $data['customer_email'],
            'customer_contact' => $data['customer_contact'],
        ]);

    $this->assertDatabaseCount($this->table, 1);

    $this->assertDatabaseHas($this->table, [
        'id' => $order->id,
        'customer_name' => $data['customer_name'],
        'customer_email' => $data['customer_email'],
        'customer_contact' => $data['customer_contact'],
    ]);
});

test('update nonexistent order returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $data = [
        'customer_name' => 'Updated Customer Name',
    ];

    $response = $this->putJson(route("{$this->routeNamePrefix}.update", ['order' => 9999]), $data)->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});

test('delete order', function () {
    Sanctum::actingAs($this->adminUser);

    $order = Order::factory()->create();

    $this->assertDatabaseCount($this->table, 1);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['order' => $order->id]))->json();

    expect($response)->toHaveSuccessGeneralResponse();

    $this->assertDatabaseCount($this->table, 0);

    $this->assertDatabaseMissing($this->table, [
        'id' => $order->id,
    ]);
});

test('delete nonexistent order returns 404', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->deleteJson(route("{$this->routeNamePrefix}.destroy", ['order' => 9999]))->json();

    expect($response)->toHaveModelResourceNotFoundResponse();
});
