<?php

use App\Models\Product;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;

beforeEach(function () {
    // Set up AutoCount configuration for testing
    Config::set('services.autocount', [
        'base_url' => 'https://autocount-api.example.com',
        'book_id' => 'book123',
        'api_key' => 'test-api-key',
        'key_id' => 'test-key-id',
    ]);

    Http::fake([
        // Stub AutoCount API response - URL pattern: {base_url}/{book_id}/product/listing
        'https://autocount-api.example.com/book123/product/listing' => Http::response([
            'data' => [
                [
                    'product' => [
                        'productId' => 4,
                        'productCode' => '18714',
                        'productName' => 'Mi Braided USB Type-C Cable 100cm (Black)',
                        'productName2' => '',
                        'productType' => 'I',
                        'unit' => '',
                        'price' => 19.00000000,
                        'minPrice' => 14.00000000,
                        'cost' => 13.70000000,
                        'productCategoryName' => null,
                        'postingGroup' => 'Default',
                        'supplyTaxCode' => null,
                        'purchaseTaxCode' => null,
                        'tariffCode' => null,
                        'status' => 'A',
                        'image' => null,
                        'furtherDescription' => null,
                        'note' => null,
                        'stockCode' => '18714',
                        'supplier' => null,
                        'variant1Name' => '',
                        'variant2Name' => '',
                        'barCode' => '6934177703584',
                        'classificationCode' => null,
                        'unitType' => null,
                        'cannotConsolidateInvoice' => false
                    ],
                    'productVariant1Options' => [],
                    'productVariant2Options' => [],
                    'productVariants' => []
                ],
                [
                    'product' => [
                        'productId' => 58,
                        'productCode' => '18863',
                        'productName' => 'Mi Braided USB Type-C Cable 100cm (Red)',
                        'productName2' => '',
                        'productType' => 'I',
                        'unit' => '',
                        'price' => 19.00000000,
                        'minPrice' => 14.00000000,
                        'cost' => 13.70000000,
                        'productCategoryName' => null,
                        'postingGroup' => 'Default',
                        'supplyTaxCode' => null,
                        'purchaseTaxCode' => null,
                        'tariffCode' => null,
                        'status' => 'A',
                        'image' => null,
                        'furtherDescription' => null,
                        'note' => null,
                        'stockCode' => '18863',
                        'supplier' => null,
                        'variant1Name' => null,
                        'variant2Name' => null,
                        'barCode' => '6934177703805',
                        'classificationCode' => null,
                        'unitType' => null,
                        'cannotConsolidateInvoice' => false
                    ],
                    'productVariant1Options' => [],
                    'productVariant2Options' => [],
                    'productVariants' => []
                ],
                [
                    'product' => [
                        // Note: no productId field - this simulates missing external_product_id
                        'productCode' => '19999',
                        'productName' => 'Product Without ID',
                        'productName2' => '',
                        'productType' => 'I',
                        'unit' => 'PCS',
                        'price' => 25.00000000,
                        'minPrice' => 20.00000000,
                        'cost' => 10.00000000,
                        'productCategoryName' => null,
                        'postingGroup' => 'Default',
                        'supplyTaxCode' => null,
                        'purchaseTaxCode' => null,
                        'tariffCode' => null,
                        'status' => 'A',
                        'image' => null,
                        'furtherDescription' => null,
                        'note' => null,
                        'stockCode' => '19999',
                        'supplier' => null,
                        'variant1Name' => '',
                        'variant2Name' => '',
                        'barCode' => null,
                        'classificationCode' => null,
                        'unitType' => null,
                        'cannotConsolidateInvoice' => false
                    ],
                    'productVariant1Options' => [],
                    'productVariant2Options' => [],
                    'productVariants' => []
                ]
            ]
        ]),
    ]);
});

test('autocount sync products creates new products', function () {
    $this->assertDatabaseCount(Product::class, 0);

    Artisan::call('autocount:sync-products');

    $this->assertDatabaseHas(Product::class, [
        'external_product_id' => '4',
        'name' => 'Mi Braided USB Type-C Cable 100cm (Black)',
        'sku' => '18714',
        'price' => 19.00,
    ]);

    $this->assertDatabaseHas(Product::class, [
        'external_product_id' => '58',
        'name' => 'Mi Braided USB Type-C Cable 100cm (Red)',
        'sku' => '18863',
        'price' => 19.00,
    ]);

    $this->assertDatabaseHas(Product::class, [
        'name' => 'Product Without ID',
        'sku' => '19999',
        'price' => 25.00,
        'external_product_id' => null,
    ]);

    $this->assertDatabaseCount(Product::class, 3);
});

test('autocount sync products updates existing products', function () {
    // Create existing product
    Product::factory()->create([
        'external_product_id' => '4',
        'name' => 'Old Product Name',
        'sku' => '18714',
        'price' => 50.00,
    ]);

    $this->assertDatabaseCount(Product::class, 1);

    Artisan::call('autocount:sync-products');

    // Should update existing product
    $this->assertDatabaseHas(Product::class, [
        'external_product_id' => '4',
        'name' => 'Mi Braided USB Type-C Cable 100cm (Black)',
        'sku' => '18714',
        'price' => 19.00,
    ]);

    // Should create new product
    $this->assertDatabaseHas(Product::class, [
        'external_product_id' => '58',
        'name' => 'Mi Braided USB Type-C Cable 100cm (Red)',
        'sku' => '18863',
        'price' => 19.00,
    ]);

    // Should also create the product without external_product_id
    $this->assertDatabaseHas(Product::class, [
        'name' => 'Product Without ID',
        'sku' => '19999',
        'price' => 25.00,
        'external_product_id' => null,
    ]);

    // No duplicate products created (1 existing + 2 new)
    $this->assertDatabaseCount(Product::class, 3);
});

test('autocount sync products dry run mode', function () {
    $this->assertDatabaseCount(Product::class, 0);

    Artisan::call('autocount:sync-products', ['--dry-run' => true]);

    // No products should be created in dry run mode
    $this->assertDatabaseCount(Product::class, 0);
});

test('autocount sync products without external_product_id creates new product', function () {
    // This test uses the mock data from beforeEach which includes a product without productId
    $this->assertDatabaseCount(Product::class, 0);

    Artisan::call('autocount:sync-products');

    // Should create the product without external_product_id
    $this->assertDatabaseHas(Product::class, [
        'name' => 'Product Without ID',
        'sku' => '19999',
        'price' => 25.00,
        'external_product_id' => null,
    ]);

    // Should create all 3 products (2 with external_product_id, 1 without)
    $this->assertDatabaseCount(Product::class, 3);
});
