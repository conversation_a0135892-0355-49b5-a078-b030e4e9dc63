<?php

use App\Enums\ClassTypePriceType;
use App\Models\ClassType;

beforeEach(function () {
    $this->classType = ClassType::factory()->create([
        'name' => 'Yoga Class',
        'description' => 'A relaxing yoga session',
        'duration_in_minutes' => 60,
        'class_count' => 1,
        'price_type' => ClassTypePriceType::FIXED,
        'price' => 25.00,
        'colour' => '#96CEB4',
        'is_addon' => false,
        'is_bookable' => true,
        'is_active' => true,
    ]);
});

describe('class type graphQL queries', function () {

    test('query returns specific class type by id', function () {
        $response = $this->graphQL('
            query($id: ID!) {
                classType(id: $id) {
                    id
                    name
                    description
                    duration_in_minutes
                    class_count
                    price_type
                    price
                    colour
                    is_addon
                    is_bookable
                    is_active
                }
            }
        ', [
            'id' => $this->classType->id,
        ]);

        $response->assertJsonStructure([
            'data' => [
                'classType' => [
                    'id',
                    'name',
                    'description',
                    'duration_in_minutes',
                    'class_count',
                    'price_type',
                    'price',
                    'colour',
                    'is_addon',
                    'is_bookable',
                    'is_active',
                ],
            ],
        ]);

        $class_type = $response->json('data.classType');

        expect($class_type)->toMatchArray([
            'id' => $this->classType->id,
            'name' => $this->classType->name,
        ]);
    });

    test('query returns null for non-existent id', function () {
        $response = $this->graphQL('
            query($id: ID!) {
                classType(id: $id) {
                    id
                    name
                }
            }
        ', [
            'id' => 99999,
        ]);

        $response->assertJson([
            'data' => [
                'classType' => null,
            ],
        ]);
    });

    test('query returns paginated class types', function () {
        $response = $this->graphQL('
            query {
                classTypes(first: 10) {
                    data {
                        id
                        name
                        description
                        duration_in_minutes
                        class_count
                        price_type
                        price
                        colour
                        is_addon
                        is_bookable
                        is_active
                    }
                    paginatorInfo {
                        count
                        currentPage
                        hasMorePages
                    }
                }
            }
        ');

        $response->assertJsonStructure([
            'data' => [
                'classTypes' => [
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'description',
                            'duration_in_minutes',
                            'class_count',
                            'price_type',
                            'price',
                            'colour',
                            'is_addon',
                            'is_bookable',
                            'is_active',
                        ],
                    ],
                    'paginatorInfo' => [
                        'count',
                        'currentPage',
                        'hasMorePages',
                    ],
                ],
            ],
        ]);

        $class_types = $response->json('data.classTypes.data');
        expect($class_types)->toHaveCount(1);
    });
});
