<?php

use App\Enums\Permission as PermissionEnum;
use App\Enums\Role as RoleEnum;
use App\Enums\OrderStatus as OrderStatusEnum;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\User;
use Laravel\Sanctum\Sanctum;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    // Create roles
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::STAFF, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::SUPER_ADMIN, 'guard_name' => 'api']);

    // Create permissions
    Permission::create(['name' => PermissionEnum::VIEW_ORDER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_ORDER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::DELETE_ORDER, 'guard_name' => 'api']);
    Permission::create(['name' => PermissionEnum::EDIT_ORDER_STATUS, 'guard_name' => 'api']);

    // Create test users
    $this->adminUser = User::factory()->create([
        'firstname' => 'Admin',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->adminUser->assignRole(RoleEnum::SUPER_ADMIN);
    $this->adminUser->givePermissionTo([
        PermissionEnum::VIEW_ORDER,
        PermissionEnum::EDIT_ORDER,
        PermissionEnum::DELETE_ORDER,
        PermissionEnum::EDIT_ORDER_STATUS,
    ]);

    $this->staffUser = User::factory()->create([
        'firstname' => 'Staff',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->staffUser->assignRole(RoleEnum::STAFF);
    $this->staffUser->givePermissionTo([PermissionEnum::VIEW_ORDER]);

    $this->customerUser = User::factory()->create([
        'firstname' => 'Customer',
        'lastname' => 'User',
        'email' => '<EMAIL>',
        'is_active' => true,
    ]);
    $this->customerUser->assignRole(RoleEnum::CUSTOMER);

    // Create order status
    $this->orderStatus = OrderStatus::factory()->create([
        'name' => 'Pending',
        'slug' => OrderStatusEnum::PENDING,
        'color' => '#FFA500',
        'serial' => 1,
        'default' => true,
    ]);

    // Create test products
    $this->product1 = Product::factory()->create([
        'name' => 'Test Product 1',
        'price' => 100.00,
        'sku' => 'TEST-001',
    ]);

    $this->product2 = Product::factory()->create([
        'name' => 'Test Product 2',
        'price' => 50.00,
        'sku' => 'TEST-002',
    ]);

    // Create test order
    $this->order = Order::factory()->create([
        'display_id' => 'ORD-001',
        'tracking_number' => 'TRK-123456',
        'customer_id' => $this->customerUser->id,
        'customer_name' => 'Customer User',
        'customer_contact' => '+1234567890',
        'customer_email' => '<EMAIL>',
        'status' => $this->orderStatus->id, // Use ID instead of slug
        'amount' => 150.00,
        'total' => 150.00,
        'paid_total' => 150.00,
        'payment_gateway' => 'stripe',
        'require_shipping' => true,
    ]);

    // Create order products
    $this->orderProduct1 = OrderProduct::factory()->create([
        'order_id' => $this->order->id,
        'product_id' => $this->product1->id,
        'name' => $this->product1->name,
        'sku' => $this->product1->sku,
        'order_quantity' => 1,
        'unit_price' => 100.00,
        'subtotal' => 100.00,
    ]);

    $this->orderProduct2 = OrderProduct::factory()->create([
        'order_id' => $this->order->id,
        'product_id' => $this->product2->id,
        'name' => $this->product2->name,
        'sku' => $this->product2->sku,
        'order_quantity' => 1,
        'unit_price' => 50.00,
        'subtotal' => 50.00,
    ]);
});

test('order query returns all fields correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            order(id: $id) {
                id
                display_id
                tracking_number
                type
                require_shipping
                customer_name
                customer_contact
                customer_email
                amount
                sales_tax
                paid_total
                total
                payment_gateway
                status {
                    slug
                    name
                }
                created_at
                updated_at

                customer {
                    id
                    firstname
                    lastname
                    email
                }

                order_products {
                    id
                    name
                    sku
                    order_quantity
                    unit_price
                    subtotal
                    product {
                        id
                        name
                    }
                }
            }
        }
    ', [
        'id' => $this->order->id,
    ]);

    $response->assertJson([
        'data' => [
            'order' => [
                'id' => (string)$this->order->id,
                'display_id' => 'ORD-001',
                'tracking_number' => 'TRK-123456',
                'customer_name' => 'Customer User',
                'customer_contact' => '+1234567890',
                'customer_email' => '<EMAIL>',
                'amount' => 150.0,
                'total' => 150.0,
                'paid_total' => 150.0,
                'payment_gateway' => 'stripe',
                'status' => [
                    'slug' => $this->orderStatus->slug,
                    'name' => $this->orderStatus->name,
                ],
                'require_shipping' => true,
                'customer' => [
                    'id' => (string)$this->customerUser->id,
                    'firstname' => 'Customer',
                    'lastname' => 'User',
                    'email' => '<EMAIL>',
                ],
                'order_products' => [
                    [
                        'id' => (string)$this->orderProduct1->id,
                        'name' => 'Test Product 1',
                        'sku' => 'TEST-001',
                        'order_quantity' => 1,
                        'unit_price' => 100.0,
                        'subtotal' => 100.0,
                        'product' => [
                            'id' => (string)$this->product1->id,
                            'name' => 'Test Product 1',
                        ],
                    ],
                    [
                        'id' => (string)$this->orderProduct2->id,
                        'name' => 'Test Product 2',
                        'sku' => 'TEST-002',
                        'order_quantity' => 1,
                        'unit_price' => 50.0,
                        'subtotal' => 50.0,
                        'product' => [
                            'id' => (string)$this->product2->id,
                            'name' => 'Test Product 2',
                        ],
                    ],
                ],
            ],
        ],
    ]);
});

test('orders query returns paginated list correctly', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query {
            orders {
                data {
                    id
                    display_id
                    tracking_number
                    customer_name
                    customer_contact
                    amount
                    total
                    status {
                        slug
                        name
                    }
                    created_at
                    customer {
                        id
                        firstname
                        lastname
                    }
                }
                paginatorInfo {
                    count
                    currentPage
                    hasMorePages
                    total
                }
            }
        }
    ');

    $response->assertJsonStructure([
        'data' => [
            'orders' => [
                'data' => [
                    '*' => [
                        'id',
                        'display_id',
                        'tracking_number',
                        'customer_name',
                        'customer_contact',
                        'amount',
                        'total',
                        'status',
                        'created_at',
                        'customer',
                    ],
                ],
                'paginatorInfo' => [
                    'count',
                    'currentPage',
                    'hasMorePages',
                    'total',
                ],
            ],
        ],
    ]);

    $orders = $response->json('data.orders.data');
    expect($orders)->toBeArray()->and($orders)->toHaveCount(1)
        ->and($orders[0])->toMatchArray([
            'id' => (string)$this->order->id,
            'display_id' => 'ORD-001',
            'tracking_number' => 'TRK-123456',
            'customer_name' => 'Customer User',
            'customer_contact' => '+1234567890',
            'amount' => 150.0,
            'total' => 150.0,
            'status' => [
                'slug' => $this->orderStatus->slug,
                'name' => $this->orderStatus->name,
            ],
            'customer' => [
                'id' => (string)$this->customerUser->id,
                'firstname' => 'Customer',
                'lastname' => 'User',
            ],
        ]);
});

test('order query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $this->graphQL('
        query($id: ID!) {
            order(id: $id) {
                id
                display_id
                customer_name
            }
        }
    ', [
        'id' => $this->order->id,
    ])->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('orders query denies access for unauthorized user', function () {
    Sanctum::actingAs($this->customerUser);

    $this->graphQL('
        query {
            orders {
                data {
                    id
                    display_id
                    customer_name
                }
            }
        }
    ')->assertGraphQLErrorMessage('This action is unauthorized.');
});

test('staff user with view permission can access orders', function () {
    Sanctum::actingAs($this->staffUser);

    $response = $this->graphQL('
        query {
            orders {
                data {
                    id
                    display_id
                    customer_name
                }
            }
        }
    ');

    $orders = $response->json('data.orders.data');
    expect($orders)->toBeArray()->and($orders)->toHaveCount(1);
});

test('unauthenticated user cannot access orders', function () {
    $this->graphQL('
        query {
            orders {
                data {
                    id
                    display_id
                    customer_name
                }
            }
        }
    ')->assertGraphQLErrorMessage('Unauthenticated.');
});

test('order query with filtering by customer_id works correctly', function () {
    Sanctum::actingAs($this->adminUser);

    // Create another customer and order
    $another_customer = User::factory()->create([
        'firstname' => 'Another',
        'lastname' => 'Customer',
        'email' => '<EMAIL>',
    ]);

    $processing_status = OrderStatus::factory()->create([
        'name' => 'Processing',
        'slug' => OrderStatusEnum::PROCESSING,
        'color' => '#0066CC',
        'serial' => 2,
        'default' => false,
    ]);

    Order::factory()->create([
        'display_id' => 'ORD-002',
        'customer_id' => $another_customer->id,
        'customer_name' => 'Another Customer',
        'amount' => 200.00,
        'total' => 200.00,
        'paid_total' => 200.00,
        'status' => $processing_status->id,
    ]);

    $response = $this->graphQL('
        query {
            orders(where: { column: CUSTOMER_ID, operator: EQ, value: "' . $this->customerUser->id . '" }) {
                data {
                    id
                    display_id
                    customer_name
                    customer {
                        id
                    }
                }
            }
        }
    ');

    $orders = $response->json('data.orders.data');
    expect($orders)->toBeArray()->and($orders)->toHaveCount(1)
        ->and($orders[0]['customer']['id'])->toBe((string)$this->customerUser->id);
});

test('order query returns correct order products relationship', function () {
    Sanctum::actingAs($this->adminUser);

    $response = $this->graphQL('
        query($id: ID!) {
            order(id: $id) {
                id
                order_products {
                    id
                    name
                    sku
                    order_quantity
                    unit_price
                    subtotal
                    product {
                        id
                        name
                        sku
                    }
                }
            }
        }
    ', [
        'id' => $this->order->id,
    ]);

    $order_products = $response->json('data.order.order_products');
    expect($order_products)->toBeArray()->and($order_products)->toHaveCount(2);

    // Verify first product
    expect($order_products[0])->toMatchArray([
        'id' => (string)$this->orderProduct1->id,
        'name' => 'Test Product 1',
        'sku' => 'TEST-001',
        'order_quantity' => 1,
        'unit_price' => 100.0,
        'subtotal' => 100.0,
        'product' => [
            'id' => (string)$this->product1->id,
            'name' => 'Test Product 1',
            'sku' => 'TEST-001',
        ],
    ]);

    // Verify second product
    expect($order_products[1])->toMatchArray([
        'id' => (string)$this->orderProduct2->id,
        'name' => 'Test Product 2',
        'sku' => 'TEST-002',
        'order_quantity' => 1,
        'unit_price' => 50.0,
        'subtotal' => 50.0,
        'product' => [
            'id' => (string)$this->product2->id,
            'name' => 'Test Product 2',
            'sku' => 'TEST-002',
        ],
    ]);
});
