<?php

use App\Models\Category;
use App\Models\Product;
use App\Models\ProductBundleItem;
use App\Models\ProductVariant;
use App\Repositories\ProductRepository;
use App\Services\ProductService;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->productService = app(ProductService::class);
    $this->table = Product::class;
    $this->variantTable = ProductVariant::class;
    $this->mediaTable = Media::class;
});

test('check repository', function () {
    expect($this->productService->getRepository())->toBeInstanceOf(ProductRepository::class);
});

describe('syncVariants', function () {
    test('sync new variants', function () {
        // Create a product first
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        $variants = [
            [
                'title' => 'Small',
                'price' => '50.00',
                'sku' => 'PROD-SMALL',
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'sku' => 'PROD-LARGE',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were created in database
        $this->assertDatabaseCount($this->variantTable, 2);
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Small',
            'price' => '50.00',
            'sku' => 'PROD-SMALL',
        ]);
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'Large',
            'price' => '80.00',
            'sku' => 'PROD-LARGE',
        ]);
    });

    test('sync existing variants', function () {
        $product = Product::factory()->create();

        // Create existing variants
        $variant1 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Small',
            'price' => '40.00',
        ]);
        $variant2 = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Large',
            'price' => '70.00',
        ]);

        $variants = [
            [
                'id' => $variant1->id,
                'title' => 'Updated Small',
                'price' => '55.00',
                'sku' => 'PROD-SMALL-UPDATED',
            ],
            [
                'id' => $variant2->id,
                'title' => 'Updated Large',
                'price' => '85.00',
                'sku' => 'PROD-LARGE-UPDATED',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were updated
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant1->id,
            'title' => 'Updated Small',
            'price' => '55.00',
            'sku' => 'PROD-SMALL-UPDATED',
        ]);
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $variant2->id,
            'title' => 'Updated Large',
            'price' => '85.00',
            'sku' => 'PROD-LARGE-UPDATED',
        ]);
    });

    test('sync new and existing variants', function () {
        $product = Product::factory()->create();

        // Create one existing variant
        $existing_variant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Existing Medium',
            'price' => '60.00',
        ]);

        $variants = [
            [
                'id' => $existing_variant->id,
                'title' => 'Updated Medium',
                'price' => '65.00',
            ],
            [
                'title' => 'New Extra Large',
                'price' => '100.00',
                'sku' => 'PROD-XL',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify existing variant was updated
        $this->assertDatabaseHas($this->variantTable, [
            'id' => $existing_variant->id,
            'title' => 'Updated Medium',
            'price' => '65.00',
        ]);

        // Verify new variant was created
        $this->assertDatabaseHas($this->variantTable, [
            'product_id' => $product->id,
            'title' => 'New Extra Large',
            'price' => '100.00',
            'sku' => 'PROD-XL',
        ]);

        $this->assertDatabaseCount($this->variantTable, 2);
    });

    test('sync empty variants', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->syncVariants([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        // No variants should be created
        $this->assertDatabaseCount($this->variantTable, 0);
    });

    test('sync new variants with gallery', function () {
        $product = Product::factory()->create([
            'name' => 'Test Product',
            'price' => '100.00',
        ]);

        $variants = [
            [
                'title' => 'Small',
                'price' => '50.00',
                'sku' => 'PROD-SMALL',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ],
            ],
            [
                'title' => 'Large',
                'price' => '80.00',
                'sku' => 'PROD-LARGE',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ],
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variants were created in database
        $this->assertDatabaseCount($this->variantTable, 2);

        // Get the created variants
        $created_variants = $product->fresh()->variants;
        expect($created_variants)->toHaveCount(2);

        // Verify gallery media for each variant
        $smallVariant = $created_variants->where('title', 'Small')->first();
        $largeVariant = $created_variants->where('title', 'Large')->first();

        expect($smallVariant->getMedia('gallery'))->toHaveCount(2)
            ->and($largeVariant->getMedia('gallery'))->toHaveCount(1);
    });

    test('sync existing variants with gallery', function () {
        $product = Product::factory()->create();
        $existing_variant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'title' => 'Original Title',
            'price' => '50.00',
        ]);

        $variants = [
            [
                'id' => $existing_variant->id,
                'title' => 'Updated Title',
                'price' => '60.00',
                'gallery' => [
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                    'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                ],
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variant was updated
        $updated_variant = $existing_variant->fresh();
        expect($updated_variant->title)->toBe('Updated Title')
            ->and($updated_variant->price)->toBe('60.00')
            ->and($updated_variant->getMedia('gallery'))->toHaveCount(2);
    });

    test('sync variants without gallery', function () {
        $product = Product::factory()->create();

        $variants = [
            [
                'title' => 'No Gallery Variant',
                'price' => '50.00',
                'sku' => 'NO-GALLERY',
            ],
        ];

        $result = $this->productService
            ->setModel($product)
            ->syncVariants($variants);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify variant was created
        $this->assertDatabaseCount($this->variantTable, 1);

        $created_variant = $product->fresh()->variants->first();
        expect($created_variant->title)->toBe('No Gallery Variant')
            ->and($created_variant->getMedia('gallery'))->toHaveCount(0);
    });
});

describe('setImage', function () {
    test('set with url', function () {
        $product = Product::factory()->create();
        $image_url = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

        $result = $this->productService
            ->setModel($product)
            ->setImage($image_url);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'image',
        ]);

        // Verify only one image in the collection (singleFile)
        expect($product->fresh()->getMedia('image'))->toHaveCount(1);
    });

    test('set with null url', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->setImage(null);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'image',
        ]);
    });
});

describe('setGallery', function () {
    test('set multiple product gallery images', function () {
        $product = Product::factory()->create();
        $gallery_urls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $result = $this->productService
            ->setModel($product)
            ->setGallery($gallery_urls);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify gallery images were created
        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'gallery',
        ]);

        // Verify correct count of gallery images
        expect($product->fresh()->getMedia('gallery'))->toHaveCount(3);
    });

    test('set empty gallery', function () {
        $product = Product::factory()->create();

        $result = $this->productService
            ->setModel($product)
            ->setGallery([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $product->id,
            'collection_name' => 'gallery',
        ]);
    });
});


describe('deleteImage', function () {
    test('deletes existing image', function () {
        $product = Product::factory()->create();
        $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

        // add then delete
        $this->productService->setModel($product)->setImage($imageUrl);
        expect($product->fresh()->getMedia('image'))->toHaveCount(1);

        $result = $this->productService->setModel($product)->deleteImage();
        expect($result)->toBeInstanceOf(ProductService::class);
        expect($product->fresh()->getMedia('image'))->toHaveCount(0);
    });
});


describe('deleteGallery', function () {
    test('deletes specific gallery items by URL', function () {
        $product = Product::factory()->create();
        $gallery_urls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        // add gallery
        $this->productService->setModel($product)->setGallery($gallery_urls);
        expect($product->fresh()->getMedia('gallery'))->toHaveCount(3);

        // delete one url by actual stored filename
        $mediaItems = $product->fresh()->getMedia('gallery');
        $target = $mediaItems[0];
        $toDelete = [getMediaFullUrl($target)];
        $result = $this->productService->setModel($product)->deleteGallery($toDelete);
        expect($result)->toBeInstanceOf(ProductService::class);

        $media = $product->fresh()->getMedia('gallery');
        expect($media)->toHaveCount(2);
    });
});

describe('syncBundleItems', function () {
    test('creates new bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        $items = [
            ['product_id' => $item1->id, 'quantity' => 2],
            ['product_id' => $item2->id, 'quantity' => 3],
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify bundle items were created
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 3,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(2);
    });

    test('updates existing bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        // Update with new quantities
        $items = [
            ['product_id' => $item1->id, 'quantity' => 5],
            ['product_id' => $item2->id, 'quantity' => 7],
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify quantities were updated
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 5,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 7,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(2);
    });

    test('deletes removed bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        // Sync with only item1 and item2 (item3 should be deleted)
        $items = [
            ['product_id' => $item1->id, 'quantity' => 1],
            ['product_id' => $item2->id, 'quantity' => 2],
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify item3 was deleted
        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
        ]);

        // Verify item1 and item2 still exist
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(2);
    });

    test('handles mixed create, update, and delete operations', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();
        $item4 = Product::factory()->create();

        // Create initial bundle items (item1 and item2)
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        // Sync: update item1, delete item2, create item3 and item4
        $items = [
            ['product_id' => $item1->id, 'quantity' => 10], // update
            ['product_id' => $item3->id, 'quantity' => 3],  // create
            ['product_id' => $item4->id, 'quantity' => 4],  // create
            // item2 is omitted, so it should be deleted
        ];

        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify item1 was updated
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 10,
        ]);

        // Verify item2 was deleted
        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);

        // Verify item3 and item4 were created
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item4->id,
            'quantity' => 4,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(3);
    });

    test('handles empty items array', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();

        // Create initial bundle item
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        // Sync with empty array (should delete all items)
        $result = $this->productService
            ->setModel($bundle_product)
            ->syncBundleItems([]);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify all items were deleted
        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(0);
    });

    test('validates bundle product requirement', function () {
        $regular_product = Product::factory()->create(['is_bundle' => false]);
        $item1 = Product::factory()->create();

        $items = [
            ['product_id' => $item1->id, 'quantity' => 2],
        ];

        // This should work even for non-bundle products (service doesn't enforce bundle requirement)
        $result = $this->productService
            ->setModel($regular_product)
            ->syncBundleItems($items);

        expect($result)->toBeInstanceOf(ProductService::class);

        // Verify item was created (service delegates to repository)
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $regular_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);
    });
});

describe('sync categories', function () {
    test('set empty categories', function () {
        $product = Product::factory()->create();

        $this->productService->setModel($product)
            ->syncCategories([]);

        expect($product->categories)->toHaveCount(0);
    });

    test('set categories', function () {
        $product = Product::factory()->create();

        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $this->productService->setModel($product)
            ->syncCategories([$category1->id, $category2->id]);

        expect($product->categories)->toHaveCount(2)
            ->and($product->categories->pluck('id'))->toContain($category1->id, $category2->id);
    });


    test('replace categories', function () {
        $product = Product::factory()->create();

        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $product->categories()->sync([$category1->id]);

        $this->productService->setModel($product)
            ->syncCategories([$category2->id]);

        expect($product->categories)->toHaveCount(1)
            ->and($product->categories->pluck('id'))->toContain($category2->id);
    });
});
