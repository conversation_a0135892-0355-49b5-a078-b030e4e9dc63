<?php

use App\Models\Product;
use App\Models\ProductBundleItem;
use App\Repositories\ProductRepository;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->product = Product::factory()->create();
    $this->productRepository = app(ProductRepository::class);
    $this->mediaTable = Media::class;
});

test('model', function () {
    expect($this->productRepository->model())->toBe(Product::class);
});

describe('setImage', function () {
    test('sets with url', function () {
        $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

        $result = $this->productRepository->setImage($this->product, $imageUrl);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $this->product->id,
            'collection_name' => 'image',
        ]);

        // Verify only one image in the collection (singleFile)
        expect($this->product->fresh()->getMedia('image'))->toHaveCount(1);
    });

    test('set with null url', function () {
        $result = $this->productRepository->setImage($this->product, null);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $this->product->id,
            'collection_name' => 'image',
        ]);
    });
});

describe('setGallery', function () {
    test('set multiple product gallery images', function () {
        $gallery_urls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $result = $this->productRepository->setGallery($this->product, $gallery_urls);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify gallery images were created
        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $this->product->id,
            'collection_name' => 'gallery',
        ]);

        // Verify correct count of gallery images
        expect($this->product->fresh()->getMedia('gallery'))->toHaveCount(3);
    });

    test(' set empty gallery', function () {
        $result = $this->productRepository->setGallery($this->product, []);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => Product::class,
            'model_id' => $this->product->id,
            'collection_name' => 'gallery',
        ]);
    });
});



describe('deleteImage', function () {
    test('deletes existing image', function () {
        $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

        // add an image first
        $this->productRepository->setImage($this->product, $imageUrl);
        expect($this->product->fresh()->getMedia('image'))->toHaveCount(1);

        // delete image
        $result = $this->productRepository->deleteImage($this->product);
        expect($result)->toBeInstanceOf(ProductRepository::class);

        // assert collection is cleared
        expect($this->product->fresh()->getMedia('image'))->toHaveCount(0);
    });
});


describe('deleteGalleryByUrls', function () {
    test('deletes specific gallery items by URL', function () {
        $gallery_urls = [
            // Use reachable URL used elsewhere in tests
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        // add gallery first
        $this->productRepository->setGallery($this->product, $gallery_urls);
        expect($this->product->fresh()->getMedia('gallery'))->toHaveCount(3);

        // Build deletion target using the actual stored media file name
        $mediaItems = $this->product->fresh()->getMedia('gallery');
        $target = $mediaItems[1]; // remove second
        $toDelete = [getMediaFullUrl($target)];

        $result = $this->productRepository->deleteGalleryByUrls($this->product, $toDelete);
        expect($result)->toBeInstanceOf(ProductRepository::class);

        $media = $this->product->fresh()->getMedia('gallery');
        expect($media)->toHaveCount(2);
    });
});

describe('syncBundleItems', function () {
    test('creates new bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        $items = [
            ['product_id' => $item1->id, 'quantity' => 2],
            ['product_id' => $item2->id, 'quantity' => 3],
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify bundle items were created
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 3,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(2);
    });

    test('updates existing bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        // Update with new quantities
        $items = [
            ['product_id' => $item1->id, 'quantity' => 5],
            ['product_id' => $item2->id, 'quantity' => 7],
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify quantities were updated
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 5,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 7,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(2);
    });

    test('deletes removed bundle items', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();

        // Create initial bundle items
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        // Sync with only item1 and item2 (item3 should be deleted)
        $items = [
            ['product_id' => $item1->id, 'quantity' => 1],
            ['product_id' => $item2->id, 'quantity' => 2],
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify item3 was deleted
        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
        ]);

        // Verify item1 and item2 still exist
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(2);
    });

    test('handles mixed create, update, and delete operations', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();
        $item3 = Product::factory()->create();
        $item4 = Product::factory()->create();

        // Create initial bundle items (item1 and item2)
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 2,
        ]);

        // Sync: update item1, delete item2, create item3 and item4
        $items = [
            ['product_id' => $item1->id, 'quantity' => 10], // update
            ['product_id' => $item3->id, 'quantity' => 3],  // create
            ['product_id' => $item4->id, 'quantity' => 4],  // create
            // item2 is omitted, so it should be deleted
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify item1 was updated
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 10,
        ]);

        // Verify item2 was deleted
        $this->assertDatabaseMissing(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
        ]);

        // Verify item3 and item4 were created
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item3->id,
            'quantity' => 3,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item4->id,
            'quantity' => 4,
        ]);

        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(3);
    });

    test('handles empty items array', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();

        // Create initial bundle item
        ProductBundleItem::create([
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);

        // Sync with empty array (should delete all items)
        $result = $this->productRepository->syncBundleItems($bundle_product, []);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify all items were deleted
        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(0);
    });

    test('handles alternative field names', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();
        $item2 = Product::factory()->create();

        // Test with item_product_id instead of product_id
        $items = [
            ['item_product_id' => $item1->id, 'quantity' => 2],
            ['product_id' => $item2->id], // quantity should default to 1
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify items were created with correct quantities
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item2->id,
            'quantity' => 1, // default quantity
        ]);
    });

    test('skips invalid items without product_id', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();

        $items = [
            ['product_id' => $item1->id, 'quantity' => 2],
            ['quantity' => 3], // missing product_id, should be skipped
            ['product_id' => 0, 'quantity' => 4], // invalid product_id, should be skipped
            ['product_id' => '', 'quantity' => 5], // empty product_id, should be skipped
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Only the valid item should be created
        expect(ProductBundleItem::where('bundle_product_id', $bundle_product->id)->count())->toBe(1);

        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 2,
        ]);
    });

    test('ensures minimum quantity of 1', function () {
        $bundle_product = Product::factory()->create(['is_bundle' => true]);
        $item1 = Product::factory()->create();

        $items = [
            ['product_id' => $item1->id, 'quantity' => 0], // should be set to 1
        ];

        $result = $this->productRepository->syncBundleItems($bundle_product, $items);

        expect($result)->toBeInstanceOf(ProductRepository::class);

        // Verify quantity was set to minimum of 1
        $this->assertDatabaseHas(ProductBundleItem::class, [
            'bundle_product_id' => $bundle_product->id,
            'item_product_id' => $item1->id,
            'quantity' => 1,
        ]);
    });
});
