<?php

use App\Models\Branch;
use App\Models\Order;
use App\Repositories\OrderRepository;

beforeEach(function () {
    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);
    $this->branch3 = Branch::factory()->create([
        'name' => 'Test Branch 3',
        'active' => true,
    ]);

    $this->order = Order::factory()->create();

    $this->orderRepository = app(OrderRepository::class);
});

test('model', function () {
    $response = $this->orderRepository->model();

    expect($response)->toEqual(Order::class);
});

describe('sync branches', function () {
    test('with new branches', function () {
        $branches = [$this->branch1->id, $this->branch2->id];

        $result = $this->orderRepository->syncBranches($this->order, $branches);

        expect($this->order->branches()->count())->toBe(2)
            ->and($this->order->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);
    });

    test('with removes old branches and adds new ones', function () {
        // Initially assign branches
        $this->order->branches()->attach([$this->branch1->id, $this->branch2->id]);
        expect($this->order->branches()->count())->toBe(2);

        // Sync with different branches (should remove old and add new)
        $this->orderRepository->syncBranches($this->order, [$this->branch3->id]);

        expect($this->order->branches()->count())->toBe(1)
            ->and($this->order->branches->first()->id)->toBe($this->branch3->id)
            ->and($this->order->branches->pluck('id')->toArray())->not()->toContain($this->branch1->id, $this->branch2->id);
    });

    test('with empty branches array', function () {
        // Initially assign some branches
        $this->order->branches()->attach([$this->branch1->id, $this->branch2->id]);
        expect($this->order->branches()->count())->toBe(2);

        // Sync with empty array (should remove all branches)
        $result = $this->orderRepository->syncBranches($this->order, []);

        expect($this->order->branches()->count())->toBe(0);
    });
});
