<?php

use App\Models\Branch;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\User;
use App\Repositories\OrderRepository;
use App\Services\OrderService;

beforeEach(function () {
    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    $this->orderService = app(OrderService::class);

    $this->table = Order::class;

    $this->customer = User::factory()->create();
    $this->customer2 = User::factory()->create();

    $this->orderStatus = OrderStatus::factory()->create();
    $this->orderStatus2 = OrderStatus::factory()->create();
});

test('check repository', function () {
    expect($this->orderService->getRepository())->toBeInstanceOf(OrderRepository::class);
});

test('sync branches', function () {
    $order = Order::factory()->create();

    $branches = [$this->branch1->id, $this->branch2->id];

    $response = $this->orderService
        ->setModel($order)
        ->syncBranches($branches);

    expect($response)->toBeInstanceOf(OrderService::class)
        ->and($order->branches()->count())->toBe(2)
        ->and($order->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);

    // Test updating branches (removing one, keeping one)
    $updated_branches = [$this->branch1->id];

    $this->orderService
        ->setModel($order)
        ->syncBranches($updated_branches);

    $order->refresh();

    expect($order->branches()->count())->toBe(1)
        ->and($order->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

test('sync products', function () {
    $product = Product::factory()->create();
    $product2 = Product::factory()->create();

    $order = Order::factory()->create();

    $product_data = [
        [
            'sku' => 'sku1',
            'product_id' => $product->id,
            'name' => 'product1',
            'order_quantity' => 1,
            'invoiced_quantity' => 2,
            'unit_price' => 3.20,
            'tax' => 3.00,
            'subtotal' => 9.00,
        ],
        [
            'sku' => 'sku2',
            'product_id' => $product2->id,
            'name' => 'product2',
            'order_quantity' => 2,
            'invoiced_quantity' => 3,
            'unit_price' => 3.40,
            'tax' => 3.50,
            'subtotal' => 19.00,
        ],
    ];

    $this->orderService->setModel($order)
        ->syncProducts($product_data);

    $order->refresh();

    expect($order->products()->count())->toBe(2)
        ->and($order->products[0]->toArray())->toMatchArray($product_data[0])
        ->and($order->products[1]->toArray())->toMatchArray($product_data[1]);

    //sync new product will remove old one
    $product_data = [
        [
            'sku' => 'sku3',
            'product_id' => null,
            'name' => 'product3',
            'order_quantity' => 12,
            'invoiced_quantity' => 21,
            'unit_price' => 3.30,
            'tax' => 4.00,
            'subtotal' => 5.00,
        ],
    ];

    $this->orderService->setModel($order)
        ->syncProducts($product_data);

    $order->refresh();

    expect($order->products()->count())->toBe(1)
        ->and($order->products[0]->toArray())->toMatchArray($product_data[0]);
});
