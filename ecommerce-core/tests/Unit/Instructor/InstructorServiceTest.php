<?php

use App\Enums\Role as RoleEnum;
use App\Models\Branch;
use App\Models\Designation;
use App\Models\Instructor;
use App\Models\User;
use App\Repositories\InstructorRepository;
use App\Services\InstructorService;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Permission\Models\Role;

beforeEach(function () {
    Role::create(['name' => RoleEnum::INSTRUCTOR, 'guard_name' => 'api']);
    Role::create(['name' => RoleEnum::CUSTOMER, 'guard_name' => 'api']);

    // Create test branches
    $this->branch1 = Branch::factory()->create([
        'name' => 'Test Branch 1',
        'active' => true,
    ]);
    $this->branch2 = Branch::factory()->create([
        'name' => 'Test Branch 2',
        'active' => true,
    ]);

    $this->designation1 = Designation::factory()->create([
        'name' => 'Test Designation 1',
    ]);

    $this->designation2 = Designation::factory()->create([
        'name' => 'Test Designation 2',
    ]);

    $this->instructorService = app(InstructorService::class);

    $this->table = Instructor::class;
    $this->mediaTable = Media::class;
    $this->userTable = User::class;
});

test('check repository', function () {
    expect($this->instructorService->getRepository())->toBeInstanceOf(InstructorRepository::class);
});

test('sync branches', function () {
    $instructor = Instructor::factory()->create();

    $branches = [$this->branch1->id, $this->branch2->id];

    $response = $this->instructorService
        ->setModel($instructor)
        ->syncBranches($branches);

    expect($response)->toBeInstanceOf(InstructorService::class)
        ->and($instructor->branches()->count())->toBe(2)
        ->and($instructor->branches->pluck('id')->toArray())->toContain($this->branch1->id, $this->branch2->id);

    // Test updating branches (removing one, keeping one)
    $updated_branches = [$this->branch1->id];

    $this->instructorService
        ->setModel($instructor)
        ->syncBranches($updated_branches);

    $instructor->refresh();

    expect($instructor->branches()->count())->toBe(1)
        ->and($instructor->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

test('set avatar', function () {
    $instructor = Instructor::factory()->create();

    $url = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

    $this->instructorService
        ->setModel($instructor)
        ->setAvatar($url);

    $this->assertDatabaseHas($this->mediaTable, [
        'model_type' => Instructor::class,
        'model_id' => $instructor->id,
        'collection_name' => 'avatar',
    ]);
});

test('sync user that already sync to another instructor ', function () {
    $user = User::factory()->create();

    $instructor = Instructor::factory()->create();

    Instructor::factory()->create([
        'user_id' => $user->id,
    ]);

    $user->assignRole(RoleEnum::INSTRUCTOR);

    expect(function () use ($instructor, $user) {
        $this->instructorService
            ->setModel($instructor)
            ->syncUser([
                'id' => $user->id,
            ]);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(15001);
    }, 'User is linked with another stylist before.');
});

test('sync user that dont have instructor role', function () {
    $user = User::factory()->create();

    $instructor = Instructor::factory()->create();

    $user->assignRole(RoleEnum::CUSTOMER);

    expect(function () use ($instructor, $user) {
        $this->instructorService
            ->setModel($instructor)
            ->syncUser([
                'id' => $user->id,
            ]);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(15002);
    }, 'Only user with Instructor role allowed to link.');
});

test('sync user with exist user', function () {
    $user = User::factory()->create();

    $instructor = Instructor::factory()->create();

    $user->assignRole(RoleEnum::INSTRUCTOR);

    $response = $this->instructorService
        ->setModel($instructor)
        ->syncUser([
            'id' => $user->id,
        ]);

    expect($response)->toBeInstanceOf(InstructorService::class);

    $this->assertDatabaseHas($this->table, [
        'id' => $instructor->id,
        'user_id' => $user->id,
    ]);
});

test('sync user with new user', function () {
    $instructor = Instructor::factory()->create();

    $data = [
        'firstname' => 'Tony',
        'lastname' => 'Lim',
        'password' => 'password',
        'email' => '<EMAIL>',
        'phone' => '+60182938111',
        'roles' => [RoleEnum::CUSTOMER],
        'branches' => [$this->branch1->id],
    ];

    $response = $this->instructorService
        ->setModel($instructor)
        ->syncUser($data);

    expect($response)->toBeInstanceOf(InstructorService::class);

    $user = User::query()->where('email', $data['email'])->first();

    $this->assertDatabaseHas($this->userTable, [
        'firstname' => $data['firstname'],
        'lastname' => $data['lastname'],
        'email' => $data['email'],
        'phone' => $data['phone'],
    ]);

    $this->assertDatabaseHas($this->table, [
        'id' => $instructor->id,
        'user_id' => $user->id,
    ]);

    expect($user->branches->pluck('id')->toArray())->toContain($this->branch1->id);
});

test('validate user that already sync to another instructor ', function () {
    $user = User::factory()->create();

    Instructor::factory()->create([
        'user_id' => $user->id,
    ]);

    $user->assignRole(RoleEnum::INSTRUCTOR);

    expect(function () use ($user) {
        $this->instructorService
            ->validateUser($user);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(15001);
    }, 'User is linked with another stylist before.');
});

test('validate user that dont have instructor role', function () {
    $user = User::factory()->create();

    $user->assignRole(RoleEnum::CUSTOMER);

    expect(function () use ($user) {
        $this->instructorService
            ->validateUser($user);
    })->toThrow(function (Exception $e) {
        expect($e->getCode())->toBe(15002);
    }, 'Only user with Instructor role allowed to link.');
});

test('reorder', function () {
    $instructor1 = Instructor::factory()->create([
        'order_column' => 1,
    ]);

    $instructor2 = Instructor::factory()->create([
        'order_column' => 2,
    ]);

    $instructor3 = Instructor::factory()->create([
        'order_column' => 3,
    ]);

    $data = [
        [
            'id' => $instructor1->id,
            'order_column' => 2,
        ],
        [
            'id' => $instructor2->id,
            'order_column' => 3,
        ],
        [
            'id' => $instructor3->id,
            'order_column' => 1,
        ],
    ];

    $this->instructorService
        ->reorder($data);

    foreach ($data as $item) {
        $this->assertDatabaseHas($this->table, [
            'id' => $item['id'],
            'order_column' => $item['order_column'],
        ]);
    }
});
