<?php

use App\Models\Product;
use App\Models\ProductVariant;
use App\Repositories\ProductVariantRepository;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->product = Product::factory()->create();
    $this->variant = ProductVariant::factory()->create(['product_id' => $this->product->id]);
    $this->variantRepository = app(ProductVariantRepository::class);
    $this->mediaTable = Media::class;
});

test('model', function () {
    expect($this->variantRepository->model())->toBe(ProductVariant::class);
});

describe('setGallery', function () {

    test('set multiple gallery images', function () {
        $galleryUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $result = $this->variantRepository->setGallery($this->variant, $galleryUrls);

        expect($result)->toBeInstanceOf(ProductVariantRepository::class);

        // Verify gallery images were created
        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => ProductVariant::class,
            'model_id' => $this->variant->id,
            'collection_name' => 'gallery',
        ]);

        // Verify correct count of gallery images
        expect($this->variant->fresh()->getMedia('gallery'))->toHaveCount(3);
    });

    test('set empty gallery', function () {
        $result = $this->variantRepository->setGallery($this->variant, []);

        expect($result)->toBeInstanceOf(ProductVariantRepository::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => ProductVariant::class,
            'model_id' => $this->variant->id,
            'collection_name' => 'gallery',
        ]);
    });

    test('replace existing gallery  ', function () {
        // Add initial gallery images
        $initialUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $this->variantRepository->setGallery($this->variant, $initialUrls);
        expect($this->variant->fresh()->getMedia('gallery'))->toHaveCount(2);

        // Add new gallery images (should replace old ones)
        $newUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $this->variantRepository->setGallery($this->variant, $newUrls);

        // Should have only the new images (3), not old + new (5)
        expect($this->variant->fresh()->getMedia('gallery'))->toHaveCount(3);
    });
});
