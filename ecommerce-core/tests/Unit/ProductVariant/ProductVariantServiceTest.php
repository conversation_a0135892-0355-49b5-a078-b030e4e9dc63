<?php

use App\Models\Product;
use App\Models\ProductVariant;
use App\Repositories\ProductVariantRepository;
use App\Services\ProductVariantService;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

beforeEach(function () {
    $this->productVariantService = app(ProductVariantService::class);
    $this->table = ProductVariant::class;
    $this->mediaTable = Media::class;
});

test('check repository', function () {
    expect($this->productVariantService->getRepository())->toBeInstanceOf(ProductVariantRepository::class);
});

test('check relationship', function () {
    $product = Product::factory()->create();
    $product_variant = ProductVariant::factory()->create([
        'product_id' => $product->id,
    ]);

    $product_variant_model = $this->productVariantService->setModel($product_variant)->getModel();

    expect($product_variant_model->toArray())->toHaveKey('product');
});

describe('setGallery', function () {
    test('set multiple product gallery images', function () {
        $product_variant = ProductVariant::factory()->create();
        $gallery_urls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $result = $this->productVariantService
            ->setModel($product_variant)
            ->setGallery($gallery_urls);

        expect($result)->toBeInstanceOf(ProductVariantService::class);

        // Verify gallery images were created
        $this->assertDatabaseHas($this->mediaTable, [
            'model_type' => ProductVariant::class,
            'model_id' => $product_variant->id,
            'collection_name' => 'gallery',
        ]);

        // Verify correct count of gallery images
        expect($product_variant->fresh()->getMedia('gallery'))->toHaveCount(3);
    });

    test('set empty gallery', function () {
        $product_variant = ProductVariant::factory()->create();

        $result = $this->productVariantService
            ->setModel($product_variant)
            ->setGallery([]);

        expect($result)->toBeInstanceOf(ProductVariantService::class);

        $this->assertDatabaseMissing($this->mediaTable, [
            'model_type' => ProductVariant::class,
            'model_id' => $product_variant->id,
            'collection_name' => 'gallery',
        ]);
    });

    test('replace existing gallery', function () {
        $product_variant = ProductVariant::factory()->create();

        // Add initial gallery images
        $initial_urls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $this->productVariantService
            ->setModel($product_variant)
            ->setGallery($initial_urls);

        expect($product_variant->fresh()->getMedia('gallery'))->toHaveCount(2);

        // Add new gallery images (should replace old ones)
        $newUrls = [
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
        ];

        $this->productVariantService
            ->setModel($product_variant)
            ->setGallery($newUrls);

        // Should have only the new images (3), not old + new (5)
        expect($product_variant->fresh()->getMedia('gallery'))->toHaveCount(3);
    });
});
