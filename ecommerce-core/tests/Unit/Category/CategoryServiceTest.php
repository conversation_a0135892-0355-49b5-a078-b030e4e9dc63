<?php

use App\Enums\ProductType;
use App\Models\Category;
use App\Repositories\CategoryRepository;
use App\Services\CategoryService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->categoryService = app(CategoryService::class);
    $this->table = Category::class;
});

test('check repository', function () {
    expect($this->categoryService->getRepository())->toBeInstanceOf(CategoryRepository::class);
});

describe('store', function () {
    test('with auto-generated slug', function () {
        $data = [
            'name' => 'Electronics & Gadgets',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
            'meta' => ['description' => 'Electronic devices'],
            'order_column' => 1,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category)->toBeInstanceOf(Category::class)
            ->toMatchArray([
                'name' => $data['name'],
                'type' => $data['type'],
                'is_active' => $data['is_active'],
                'order_column' => $data['order_column'],
                'meta' => $data['meta'],
            ]);


        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'type' => $data['type'],
            'is_active' => $data['is_active'],
            'order_column' => $data['order_column'],
            'meta->description' => $data['meta']['description'],
        ]);
    });

    test('with unique slug when duplicate exists', function () {
        // Create first category
        Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Electronics',
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->slug)->not()->toBe('electronics')
            ->and($category->slug)->toStartWith('electronics-');

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'slug' => $category->slug,
        ]);
    });

    test('generates slug from name with special characters', function () {
        $data = [
            'name' => 'Fashion & Clothing - Women\'s Wear!',
            'type' => ProductType::DEFAULT,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->slug)->toBe('fashion-clothing-women-s-wear');
    });

    test('handles multiple duplicate slugs correctly', function () {
        // Create multiple categories with same name
        Category::factory()->create(['name' => 'Books', 'slug' => 'books']);
        Category::factory()->create(['name' => 'Books', 'slug' => 'books-1']);

        $data = [
            'name' => 'Books',
            'type' => ProductType::DEFAULT,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category->slug)->toBe('books-3');
    });

    test('with parent', function () {
        // Create parent category
        $parent = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $data = [
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
            'type' => ProductType::DEFAULT,
            'is_active' => true,
        ];

        $category = $this->categoryService->store($data)->getModel();

        expect($category)->toMatchArray([
            'name' => $data['name'],
            'parent_id' => $data['parent_id'],
            'type' => $data['type'],
            'is_active' => $data['is_active'],
        ]);

        $this->assertDatabaseHas($this->table, [
            'name' => $data['name'],
            'parent_id' => $data['parent_id'],
            'type' => $data['type'],
            'is_active' => $data['is_active'],
        ]);
    });
});

describe('update', function () {
    test('regenerate slug', function () {
        $category = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $update_data = [
            'name' => 'Electronics & Technology',
            'is_active' => false,
            'meta' => ['updated' => true],
        ];

        $updatedCategory = $this->categoryService
            ->setModel($category)
            ->update($update_data)
            ->getModel();

        expect($updatedCategory)->toMatchArray([
            'name' => $update_data['name'],
            'is_active' => $update_data['is_active'],
            'meta' => $update_data['meta'],
        ]);

        $this->assertDatabaseHas($this->table, [
            'id' => $category->id,
            'name' => $update_data['name'],
            'slug' => 'electronics-technology',
            'is_active' => $update_data['is_active'],
        ]);
    });

    test('does not change slug if name unchanged', function () {
        $category = Category::factory()->create([
            'name' => 'Electronics',
            'slug' => 'electronics',
        ]);

        $updateData = [
            'is_active' => false,
            'meta' => ['updated' => true],
        ];

        $updatedCategory = $this->categoryService
            ->setModel($category)
            ->update($updateData)
            ->getModel();

        expect($updatedCategory->slug)->toBe('electronics'); // Unchanged
    });

    test('non-existent category', function () {
        $this->categoryService->setModel(new Category(['id' => 999]));

        expect(fn() => $this->categoryService->update(['name' => 'Test']))
            ->toThrow(Exception::class);
    });
});

describe('delete', function () {
    test('delete without children', function () {
        $category = Category::factory()->create();

        $this->assertDatabaseHas($this->table, ['id' => $category->id]);

        $this->categoryService->setModel($category)->delete();

        $this->assertSoftDeleted($this->table, ['id' => $category->id]);
    });

    test('delete with children', function () {
        $parent = Category::factory()->create(['name' => 'Electronics']);
        Category::factory()->create([
            'name' => 'Smartphones',
            'parent_id' => $parent->id,
        ]);

        expect(function () use ($parent) {
            $this->categoryService->setModel($parent)->delete();
        })->toThrow(function (Exception $e) {
            expect($e->getCode())->toBe(10001);
        }, 'This data cannot be deleted because it is being used.');
    });

    test('non-existent category', function () {
        $this->categoryService->setModel(new Category(['id' => 999]));

        expect(fn() => $this->categoryService->delete())
            ->toThrow(Exception::class);
    });
});

test('set image', function () {
    $category = Category::factory()->create();
    $imageUrl = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

    $result = $this->categoryService
        ->setModel($category)
        ->setImage($imageUrl);

    expect($result)->toBeInstanceOf(CategoryService::class);

    $this->assertDatabaseHas('media', [
        'model_type' => Category::class,
        'model_id' => $category->id,
        'collection_name' => 'image',
    ]);
});
