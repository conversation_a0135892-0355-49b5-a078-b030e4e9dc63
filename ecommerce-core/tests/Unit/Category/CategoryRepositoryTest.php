<?php

use App\Models\Category;
use App\Repositories\CategoryRepository;

beforeEach(function () {
    $this->categoryRepository = app(CategoryRepository::class);

    $this->category = Category::factory()->create();
});

test('model', function () {
    expect($this->categoryRepository->model())->toBe(Category::class);
});

test('set image', function () {
    $category = Category::factory()->create();
    $image_url = 'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png';

    $result = $this->categoryRepository->setImage($category, $image_url);
    expect($result)->toBeInstanceOf(CategoryRepository::class);
    $this->assertDatabaseHas('media', [
        'model_type' => Category::class,
        'model_id' => $category->id,
        'collection_name' => 'image',
    ]);
});
