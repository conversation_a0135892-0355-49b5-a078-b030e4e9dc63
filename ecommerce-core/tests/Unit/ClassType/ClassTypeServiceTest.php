<?php

use App\Models\Category;
use App\Models\ClassType;
use App\Models\Product;
use App\Repositories\ClassTypeRepository;
use App\Services\ClassTypeService;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->classTypeService = app(ClassTypeService::class);
    $this->classTypeTable = ClassType::class;
    $this->productTable = Product::class;
    $this->categoryTable = Category::class;
    $this->categoryProductTable = 'category_product';
});

test('check repository', function () {
    expect($this->classTypeService->getRepository())->toBeInstanceOf(ClassTypeRepository::class);
});

describe('sync categories', function () {

    test('sync to the associated product', function () {
        // Create a class type (which should create a product via Productable trait)
        $classType = ClassType::factory()->create([
            'name' => 'Test Yoga Class',
            'price' => '25.00',
            'is_active' => true,
        ]);

        // Ensure product was created
        expect($classType->product)->not()->toBeNull();

        // Create categories
        $category1 = Category::factory()->create(['name' => 'Fitness']);
        $category2 = Category::factory()->create(['name' => 'Yoga']);
        $category3 = Category::factory()->create(['name' => 'Wellness']);

        $categoryIds = [$category1->id, $category2->id, $category3->id];

        // Sync categories using the service
        $result = $this->classTypeService
            ->setModel($classType)
            ->syncCategories($categoryIds);

        // Verify the service returns itself for method chaining
        expect($result)->toBeInstanceOf(ClassTypeService::class);

        // Verify categories are synced to the product
        $classType->refresh();
        $productCategories = $classType->product->categories;

        expect($productCategories)->toHaveCount(3);

        $categoryNames = $productCategories->pluck('name')->toArray();
        expect($categoryNames)->toContain('Fitness')
            ->and($categoryNames)->toContain('Yoga')
            ->and($categoryNames)->toContain('Wellness');

        // Verify the pivot table has the correct entries
        $this->assertDatabaseHas($this->categoryProductTable, [
            'product_id' => $classType->product->id,
            'category_id' => $category1->id,
        ]);
        $this->assertDatabaseHas($this->categoryProductTable, [
            'product_id' => $classType->product->id,
            'category_id' => $category2->id,
        ]);
        $this->assertDatabaseHas($this->categoryProductTable, [
            'product_id' => $classType->product->id,
            'category_id' => $category3->id,
        ]);
    });

    test('replaces existing categories', function () {
        // Create a class type with product
        $classType = ClassType::factory()->create([
            'name' => 'Test Class with Categories',
            'is_active' => true,
        ]);

        // Create categories
        $oldCategory1 = Category::factory()->create(['name' => 'Old Category 1']);
        $oldCategory2 = Category::factory()->create(['name' => 'Old Category 2']);
        $newCategory1 = Category::factory()->create(['name' => 'New Category 1']);
        $newCategory2 = Category::factory()->create(['name' => 'New Category 2']);

        // First sync with old categories
        $this->classTypeService
            ->setModel($classType)
            ->syncCategories([$oldCategory1->id, $oldCategory2->id]);

        // Verify old categories are attached
        expect($classType->product->categories)->toHaveCount(2);

        // Now sync with new categories (should replace old ones)
        $this->classTypeService
            ->setModel($classType)
            ->syncCategories([$newCategory1->id, $newCategory2->id]);

        // Refresh and verify
        $classType->refresh();
        $productCategories = $classType->product->categories;

        expect($productCategories)->toHaveCount(2);

        $categoryNames = $productCategories->pluck('name')->toArray();
        expect($categoryNames)->toContain('New Category 1')
            ->and($categoryNames)->toContain('New Category 2')
            ->and($categoryNames)->not()->toContain('Old Category 1')
            ->and($categoryNames)->not()->toContain('Old Category 2');
    });

    test('with empty array removes all categories', function () {
        // Create a class type with product
        $classType = ClassType::factory()->create([
            'name' => 'Test Class to Clear Categories',
            'is_active' => true,
        ]);

        // Create and attach categories
        $category1 = Category::factory()->create(['name' => 'Category to Remove 1']);
        $category2 = Category::factory()->create(['name' => 'Category to Remove 2']);

        $this->classTypeService
            ->setModel($classType)
            ->syncCategories([$category1->id, $category2->id]);

        // Verify categories are attached
        expect($classType->product->categories)->toHaveCount(2);

        // Sync with empty array
        $this->classTypeService
            ->setModel($classType)
            ->syncCategories([]);

        // Verify all categories are removed
        $classType->refresh();
        expect($classType->product->categories)->toHaveCount(0);

        // Verify pivot table entries are removed
        $this->assertDatabaseMissing($this->categoryProductTable, [
            'product_id' => $classType->product->id,
            'category_id' => $category1->id,
        ]);
        $this->assertDatabaseMissing($this->categoryProductTable, [
            'product_id' => $classType->product->id,
            'category_id' => $category2->id,
        ]);
    });

    test('handles class type without product gracefully', function () {
        // Create a class type without triggering product creation
        $classType = new ClassType([
            'name' => 'Test Class Without Product',
            'duration_in_minutes' => 60,
            'class_count' => 1,
            'price_type' => 'fixed',
            'price' => '30.00',
            'colour' => '#FF0000',
            'is_addon' => false,
            'is_bookable' => true,
            'is_active' => true,
        ]);

        // Save without triggering events (no product will be created)
        $classType->saveQuietly();

        // Verify no product exists
        expect($classType->product)->toBeNull();

        // Create categories
        $category1 = Category::factory()->create(['name' => 'Test Category']);

        // Attempt to sync categories - should not throw error
        $result = $this->classTypeService
            ->setModel($classType)
            ->syncCategories([$category1->id]);

        // Should return the service instance
        expect($result)->toBeInstanceOf(ClassTypeService::class);

        // No categories should be synced since there's no product
        $this->assertDatabaseMissing($this->categoryProductTable, [
            'category_id' => $category1->id,
        ]);
    });

    test('with invalid category ids is handled by database constraints', function () {
        // Create a class type with product
        $classType = ClassType::factory()->create([
            'name' => 'Test Class for Invalid Categories',
            'is_active' => true,
        ]);

        // Attempt to sync with non-existent category ID
        // This should throw a database constraint error
        expect(function () use ($classType) {
            $this->classTypeService
                ->setModel($classType)
                ->syncCategories([99999]); // Non-existent category ID
        })->toThrow(QueryException::class);
    });
});
