<?php

use App\Models\NotificationTemplate;
use App\Repositories\NotificationTemplateRepository;

beforeEach(function () {
    $this->notificationTemplateRepository = app(NotificationTemplateRepository::class);
});

test('model returns correct class', function () {
    $response = $this->notificationTemplateRepository->model();

    expect($response)->toEqual(NotificationTemplate::class);
});

test('can create notification template using repository', function () {
    $data = [
        'name' => 'test_template',
        'channel' => 'mail',
        'locale' => 'en',
        'content' => 'Test content',
        'is_active' => true,
    ];

    $template = $this->notificationTemplateRepository->create($data);

    expect($template)->toBeInstanceOf(NotificationTemplate::class)
        ->and($template->name)->toBe('test_template')
        ->and($template->channel)->toBe('mail')
        ->and($template->locale)->toBe('en')
        ->and($template->content)->toBe('Test content')
        ->and($template->is_active)->toBeTrue();

    $this->assertDatabaseHas('notification_templates', [
        'name' => 'test_template',
        'channel' => 'mail',
        'locale' => 'en',
        'content' => 'Test content',
        'is_active' => true,
    ]);
});

test('can find notification template by id', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'findable_template',
        'channel' => 'sms',
        'locale' => 'en',
        'content' => 'Findable content',
    ]);

    $found = $this->notificationTemplateRepository->find($template->id);

    expect($found)->toBeInstanceOf(NotificationTemplate::class)
        ->and($found->id)->toBe($template->id)
        ->and($found->name)->toBe('findable_template');
});

test('can update notification template using repository', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'old_name',
        'content' => 'Old content',
    ]);

    $updateData = [
        'name' => 'updated_name',
        'content' => 'Updated content',
    ];

    $updated = $this->notificationTemplateRepository->update($updateData, $template->id);

    expect($updated)->toBeInstanceOf(NotificationTemplate::class)
        ->and($updated->name)->toBe('updated_name')
        ->and($updated->content)->toBe('Updated content');

    $this->assertDatabaseHas('notification_templates', [
        'id' => $template->id,
        'name' => 'updated_name',
        'content' => 'Updated content',
    ]);
});

test('can delete notification template using repository', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'deletable_template',
    ]);

    $this->assertDatabaseHas('notification_templates', [
        'id' => $template->id,
        'name' => 'deletable_template',
    ]);

    $this->notificationTemplateRepository->delete($template->id);

    $this->assertDatabaseMissing('notification_templates', [
        'id' => $template->id,
    ]);
});

test('can count notification templates', function () {
    NotificationTemplate::factory()->count(3)->create();

    $count = $this->notificationTemplateRepository->count();

    expect($count)->toBe(3);
});

test('can find notification template by field', function () {
    $template = NotificationTemplate::factory()->create([
        'name' => 'unique_template_name',
        'channel' => 'mail',
    ]);

    $found = $this->notificationTemplateRepository->findOneByField('name', 'unique_template_name');

    expect($found)->toBeInstanceOf(NotificationTemplate::class)
        ->and($found->id)->toBe($template->id)
        ->and($found->name)->toBe('unique_template_name');
});

test('findOneByField returns null when no match found', function () {
    $found = $this->notificationTemplateRepository->findOneByField('name', 'non_existent_template');

    expect($found)->toBeNull();
});
