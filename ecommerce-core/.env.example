APP_NAME="Ecommerce Core"
APP_ENV=local
APP_DEBUG=true
APP_KEY=base64:1WFyKPUQ/icZLHJUAO9UTzVWchoNC31PnRODVauJDPU=
APP_URL=http://localhost:8000
DUMMY_DATA_PATH=pickbazar

LOG_CHANNEL=stack
LOG_LEVEL=debug

QUEUE_CONNECTION=sync
CACHE_DRIVER=file

AWS_DEFAULT_REGION="ap-southeast-1"

DB_CONNECTION=pgsql
DB_PORT=5432
DB_HOST=127.0.0.1
DB_DATABASE=ecommerce_core_testing
DB_USERNAME=postgres
DB_PASSWORD=password

FILESYSTEM_DRIVER=s3
MEDIA_DISK=s3

SHOP_URL=http://localhost:2400
DASHBOARD_URL=http://localhost:2401
ADMIN_LOGIN_REF_FLAG=false

SQS_PREFIX=http://localhost:4566/000000000000
SQS_QUEUE=cocodry-local-queue
SQS_SUFFIX=''

# mailtrap
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=ddb7c37aa9ac5f
MAIL_PASSWORD=db7f7c640ba6fe
MAIL_ENCRYPTION=tls
MAIL_FROM_NAME="${APP_NAME}"

SHOP_PAYMENT_CALLBACK_URL="${SHOP_URL}/checkout/callback"
SHOP_PAYMENT_RESULT_URL="${SHOP_URL}/checkout/result"
SHOP_BOOKING_CONFIRM_URL="${SHOP_URL}/booking-confirmation"

APP_NOTICE_DOMAIN=STUDIO_

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY="WmMs6Dg3sQWrB8UkIuio4lWAw4fFJ4dsS/ZAiPLA"
AWS_BUCKET="cocodry-dev"
APP_AWS_PREFIX_S3=/

DYNAMODB_CONNECTION=local
DYNAMODB_LOCAL_ENDPOINT=http://localhost:9000
DYNAMODB_PREFIX=''
DYNAMODB_AKEY_ID="DUMMYIDEXAMPLE"
DYNAMODB_ASECRET_KEY="DUMMYEXAMPLEKEY"
DYNAMODB_TABLE=cocodry_dev

ADMIN_EMAIL=<EMAIL>
SHOP_ADDRESS="Unit G-01 Unit, 1.1, Platinum Park,50088, Kuala Lumpur"
MAIL_FROM_ADDRESS="${ADMIN_EMAIL}"

CURLEC_API_BASE_URL=https://demo.curlec.com/
CURLEC_MERCHANT_ID=*********
CURLEC_EMPLOYEE_ID=12766497
CURLEC_API_KEY=rzp_test_FHkFVSCPI4aooH
CURLEC_API_SECRET=MFd9PalNuJQjfSVf7JuqVMRQ

DELYVA_API_KEY=
DELYVA_CUSTOMER_ID=
DELYVA_COMPANY_ID=
DELYVA_WEBHOOK_TOKEN=
DELYVA_SUBDOMAIN=

VERIFY_EXPIRE_SECONDS=300
VERIFY_TIMEOUT_SECONDS=60
VERIFY_TOKEN_EXPIRE_SECONDS=900

SENDPULSE_CLIENT_ID=
SENDPULSE_CLIENT_SECRET=
SENDPULSE_WHATSAPP_BOT_ID=
SENDPULSE_SMS_SENDER=

CLASSPASS_API_URL=https://sandbox-api.classpass.com
CLASSPASS_API_TOKEN=a42ryUs7vy4GVA2TcqAb
CLASSPASS_INTEGRATOR_TOKEN=1232434354

GRAPHQL_PLAYGROUND_ENABLED=true

SITEGIANT_BASE_URL=https://opensgapi.sitegiant.co/api/v1
SITEGIANT_SECRET_KEY=
SITEGIANT_STORE_EMAIL=
SITEGIANT_PARTNER_TOKEN=
