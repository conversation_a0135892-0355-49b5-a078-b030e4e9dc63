<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_statuses', function (Blueprint $table) {
            $table->id();

            // Status information
            $table->string('name');
            $table->string('slug')->nullable();
            $table->integer('serial');
            $table->string('color')->nullable();
            $table->boolean('default')->default(false);

            $table->timestamps();

            // Indexes for performance
            $table->index('slug');
            $table->index('serial');
            $table->index('default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_statuses');
    }
};
