<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

            // Order identification
            $table->string('display_id')->nullable();
            $table->string('external_order_id')->nullable();
            $table->string('auth_ref_id')->nullable();
            $table->string('auth_token')->nullable();
            $table->string('tracking_number')->nullable()->unique();
            $table->string('type')->nullable();
            $table->boolean('require_shipping')->default(true);

            // Customer information
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('sale_by_id')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('customer_contact')->nullable();
            $table->string('customer_email')->nullable();

            // Order status and relationships
            $table->unsignedBigInteger('status');
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->unsignedBigInteger('shop_id')->nullable();
            $table->unsignedBigInteger('coupon_id')->nullable();

            // Financial information
            $table->double('amount');
            $table->decimal('sales_tax', 10, 2)->default(0.00);
            $table->decimal('paid_total', 10, 2)->default(0.00);
            $table->decimal('total', 10, 2)->default(0.00);
            $table->decimal('total_invoiced', 10, 2)->default(0.00);
            $table->decimal('total_refunded', 10, 2)->default(0.00);

            // Discounts and promotions
            $table->decimal('coupon_discount', 10, 2)->default(0.00);
            $table->decimal('discount', 10, 2)->default(0.00);

            // Points system
            $table->decimal('points_earn', 10, 2)->default(0.00);
            $table->decimal('points_used', 10, 2)->default(0.00);
            $table->decimal('points_discount', 10, 2)->default(0.00);

            // Golds system
            $table->decimal('golds_earn', 10, 2)->default(0.00);
            $table->decimal('golds_used', 10, 2)->default(0.00);
            $table->decimal('golds_discount', 10, 2)->default(0.00);

            // Store credit and gift cards
            $table->decimal('store_credit_earn', 10, 2)->default(0.00);
            $table->decimal('store_credit', 10, 2)->default(0.00);
            $table->double('gift_card_credit', 10, 2)->default(0.00);

            // Payment information
            $table->unsignedBigInteger('payment_method_id')->nullable();
            $table->json('payment_method_info')->nullable();
            $table->string('payment_id')->nullable();
            $table->string('payment_gateway')->nullable();

            // Address information
            $table->json('shipping_address')->nullable();
            $table->json('billing_address')->nullable();

            // Shipping and delivery
            $table->unsignedBigInteger('state_id')->nullable();
            $table->unsignedBigInteger('shipping_class_id')->nullable();
            $table->string('logistics_provider')->nullable();
            $table->decimal('items_weight', 10, 3)->default(0.000);
            $table->decimal('delivery_fee', 10, 2)->default(0.00);
            $table->string('delivery_remarks')->nullable();
            $table->string('delivery_time')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Foreign key constraints
            $table->foreign('customer_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('sale_by_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_id')->references('id')->on('orders')->onDelete('cascade');
            $table->foreign('shop_id')->references('id')->on('shops')->onDelete('cascade');
            $table->foreign('status')->references('id')->on('order_statuses');

            // Indexes for performance
            $table->index(['customer_id', 'status']);
            $table->index(['shop_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('payment_gateway');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
