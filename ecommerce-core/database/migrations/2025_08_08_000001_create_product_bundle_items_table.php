<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_bundle_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('bundle_product_id');
            $table->unsignedBigInteger('item_product_id');
            $table->unsignedInteger('quantity')->default(1);
            $table->timestamps();

            $table->foreign('bundle_product_id')
                ->references('id')
                ->on('products')
                ->onDelete('cascade');

            $table->foreign('item_product_id')
                ->references('id')
                ->on('products')
                ->onDelete('cascade');

            $table->unique(['bundle_product_id', 'item_product_id'], 'uq_bundle_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_bundle_items');
    }
};
