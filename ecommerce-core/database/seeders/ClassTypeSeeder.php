<?php

namespace Database\Seeders;

use App\Enums\ClassTypePriceType;
use App\Models\ClassType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ClassTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create predefined class types for fitness/education
        $classTypes = [
            [
                'name' => 'Yoga Flow',
                'description' => 'A dynamic sequence of poses that flow smoothly together, synchronized with breath. Perfect for building strength, flexibility, and mindfulness.',
                'duration_in_minutes' => 60,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 25.00,
                'colour' => '#96CEB4',
            ],
            [
                'name' => 'HIIT Training',
                'description' => 'High-intensity interval training designed to burn calories, build strength, and improve cardiovascular fitness in a short amount of time.',
                'duration_in_minutes' => 45,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 30.00,
                'colour' => '#FF6B6B',
            ],
            [
                'name' => 'Pilates Core',
                'description' => 'Focus on core strength, stability, and posture through controlled movements and precise breathing techniques.',
                'duration_in_minutes' => 50,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 28.00,
                'colour' => '#4ECDC4',
            ],
            [
                'name' => 'Meditation',
                'description' => 'Guided meditation sessions to reduce stress, improve focus, and cultivate inner peace through various mindfulness techniques.',
                'duration_in_minutes' => 30,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 15.00,
                'colour' => '#DDA0DD',
            ],
            [
                'name' => 'Spin Class',
                'description' => 'High-energy indoor cycling class with motivating music and varied terrain simulation for an excellent cardiovascular workout.',
                'duration_in_minutes' => 45,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 22.00,
                'colour' => '#45B7D1',
            ],
            [
                'name' => 'Swimming Lessons',
                'description' => 'Learn proper swimming techniques, improve stroke efficiency, and build water confidence in a supportive environment.',
                'duration_in_minutes' => 60,
                'class_count' => 4,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 120.00,
                'colour' => '#98D8C8',
            ],
            [
                'name' => 'Personal Training',
                'description' => 'Customized fitness training session tailored to your specific goals, fitness level, and preferences.',
                'duration_in_minutes' => 60,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 80.00,
                'colour' => '#FFEAA7',
                'is_addon' => true,
            ],
            [
                'name' => 'Nutrition Workshop',
                'description' => 'Educational workshop covering nutrition basics, meal planning, and healthy eating habits for optimal wellness.',
                'duration_in_minutes' => 90,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 35.00,
                'colour' => '#F7DC6F',
            ],
            [
                'name' => 'Strength Training',
                'description' => 'Learn proper form and technique for weight training exercises to build strength and muscle safely.',
                'duration_in_minutes' => 75,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 32.00,
                'colour' => '#E74C3C',
            ],
            [
                'name' => 'Flexibility & Stretching',
                'description' => 'Improve flexibility, mobility, and range of motion through guided stretching and mobility exercises.',
                'duration_in_minutes' => 45,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FIXED,
                'price' => 20.00,
                'colour' => '#9B59B6',
            ],
            [
                'name' => 'Free Trial Class',
                'description' => 'Try any of our regular classes for free to see if our fitness programs are right for you.',
                'duration_in_minutes' => 60,
                'class_count' => 1,
                'price_type' => ClassTypePriceType::FREE,
                'price' => null,
                'colour' => '#27AE60',
            ],
        ];

        foreach ($classTypes as $classType) {
            // Set default values
            $classType = array_merge([
                'tax_class_id' => null,
                'is_addon' => false,
                'is_bookable' => true,
                'is_active' => true,
            ], $classType);

            ClassType::create($classType);
        }

        // Create some additional random class types using factory
        ClassType::factory(8)->create();

        // Create some specific variations
        ClassType::factory(2)->addon()->create();
        ClassType::factory(1)->free()->create();
    }
}
