<?php

namespace Database\Factories;

use App\Models\PaymentGateway;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentMethodFactory extends Factory
{

    public function definition()
    {
        return [
            'gateway_id' => PaymentGateway::factory(),
            'title' => fake()->name,
            'code' => fake()->slug,
            'description' => fake()->text(100),
            'payment_info' => [
                'merchant' => fake()->name,
            ],
            'is_active' => 1,
            'is_visible' => 1,
        ];
    }

}
