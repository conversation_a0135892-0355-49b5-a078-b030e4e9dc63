<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentGatewayFactory extends Factory
{

    public function definition()
    {
        return [
            'title' => fake()->name,
            'code' => fake()->slug,
            'credentials' => [
                'merchant_id' => uniqid(),
                'secret_key' => uniqid(),
            ],
            'support_email' => fake()->email,
            'is_active' => 1,
            'is_visible' => 1,
        ];
    }

}
