<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'name' => fake()->words(3, true),
            'description' => fake()->paragraph(),
            'price' => fake()->randomFloat(2, 10, 1000),
            'sale_price' => null,
            'sku' => fake()->unique()->bothify('SKU-####-????'),
            'barcode' => fake()->ean13(),
            'is_taxable' => fake()->boolean(70), // 70% chance of being taxable
            'tax_class_id' => null,
            'shipping_class_id' => null,
            'meta' => null,
            'status' => fake()->randomElement(['publish', 'draft', 'inactive']),
            'unit' => fake()->randomElement(['piece', 'kg', 'liter', 'meter']),
            'height' => fake()->randomFloat(1, 1, 100),
            'width' => fake()->randomFloat(1, 1, 100),
            'length' => fake()->randomFloat(1, 1, 100),
            'weight' => fake()->randomFloat(3, 0.1, 50),
        ];
    }

    /**
     * Indicate that the product should be published.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function published()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'publish',
            ];
        });
    }

    /**
     * Indicate that the product should be draft.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function draft()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'draft',
            ];
        });
    }

    /**
     * Indicate that the product should be inactive.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'inactive',
            ];
        });
    }

    /**
     * Indicate that the product should have a sale price.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function onSale()
    {
        return $this->state(function (array $attributes) {
            $price = $attributes['price'] ?? 100;
            return [
                'sale_price' => $price * 0.8, // 20% discount
            ];
        });
    }

    /**
     * Indicate that the product should be taxable.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function taxable()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_taxable' => true,
            ];
        });
    }

    /**
     * Indicate that the product should not be taxable.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function nonTaxable()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_taxable' => false,
            ];
        });
    }

    /**
     * Indicate that the product should have meta data.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withMeta()
    {
        return $this->state(function (array $attributes) {
            return [
                'meta' => [
                    'featured' => fake()->boolean(),
                    'tags' => fake()->words(3),
                    'color' => fake()->colorName(),
                ],
            ];
        });
    }

    /**
     * Configure the product with media after creation.
     * Use this with afterCreating() callback to add media.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withImage()
    {
        return $this->afterCreating(function (Product $product) {
            $product->addMediaFromUrl('https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png')
                ->toMediaCollection('image');
        });
    }

    /**
     * Configure the product with gallery media after creation.
     * Use this with afterCreating() callback to add media.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function withGallery()
    {
        return $this->afterCreating(function (Product $product) {
            $galleryUrls = [
                'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
                'https://cocodry-dev.s3.ap-southeast-1.amazonaws.com/images.png',
            ];

            foreach ($galleryUrls as $url) {
                $product->addMediaFromUrl($url)->toMediaCollection('gallery');
            }
        });
    }
}
