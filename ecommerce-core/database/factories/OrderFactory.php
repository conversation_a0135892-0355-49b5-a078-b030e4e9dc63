<?php

namespace Database\Factories;

use App\Enums\OrderType;
use App\Models\Order;
use App\Models\OrderStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = $this->faker->randomFloat(2, 10, 500);
        $salesTax = $amount * 0.1; // 10% tax
        $total = $amount + $salesTax;

        return [
            'display_id' => 'ORD-' . $this->faker->unique()->numberBetween(1000, 9999),
            'tracking_number' => 'TRK' . strtoupper($this->faker->unique()->bothify('??###??')),
            'type' => $this->faker->randomElement(OrderType::getValues()),
            'require_shipping' => 1,
            'customer_id' => User::factory(),
            'customer_name' => $this->faker->name(),
            'customer_contact' => $this->faker->phoneNumber(),
            'customer_email' => $this->faker->email(),
            'status' => OrderStatus::factory(),
            'amount' => $amount,
            'sales_tax' => $salesTax,
            'total' => $total,
            'paid_total' => $this->faker->boolean(70) ? $total : 0,
            'payment_gateway' => $this->faker->randomElement(['stripe', 'paypal', 'square']),
            'shipping_address' => [
                'street' => $this->faker->streetAddress(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
                'country' => $this->faker->country(),
            ],
            'billing_address' => [
                'street' => $this->faker->streetAddress(),
                'city' => $this->faker->city(),
                'state' => $this->faker->state(),
                'postal_code' => $this->faker->postcode(),
                'country' => $this->faker->country(),
            ],
            'items_weight' => $this->faker->randomFloat(3, 0.1, 10),
            'delivery_fee' => $this->faker->randomFloat(2, 0, 25),
        ];
    }
}
