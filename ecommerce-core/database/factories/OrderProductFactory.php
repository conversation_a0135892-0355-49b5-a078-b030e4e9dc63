<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Product;
use App\Models\ProductVariant;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderProductFactory extends Factory
{

    public function definition()
    {
        return [
            'order_id' => Order::factory(),
            'product_id' => Product::factory(),
            'product_variant_id' => ProductVariant::factory(),
            'name' => fake()->name,
            'sku' => fake()->unique()->bothify('SKU-####-????'),
            'barcode' => fake()->ean13(),
            'height' => fake()->randomFloat(1, 1, 100),
            'width' => fake()->randomFloat(1, 1, 100),
            'length' => fake()->randomFloat(1, 1, 100),
            'weight' => fake()->randomFloat(3, 0.1, 50),
            'order_quantity' => 1,
            'invoiced_quantity' => 1,
            'shipped_quantity' => 1,
            'canceled_quantity' => 1,
            'refunded_quantity' => 1,
            'unit_price' => fake()->numberBetween(1, 100),
            'upfront_amount' => fake()->numberBetween(1, 100),
            'discount_rate_type' => 'percentage',
            'discount_rate' => fake()->numberBetween(1, 100),
            'discount' => fake()->numberBetween(1, 100),
            'store_credit' => fake()->numberBetween(1, 100),
            'gift_card_credit' => fake()->numberBetween(1, 100),
            'subtotal' => fake()->numberBetween(1, 100),
            'tax' => fake()->numberBetween(1, 100),
            'is_commissioned' => 0,
            'is_deposit' => 0,
        ];
    }

}
