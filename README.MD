## Module Structure

1. Controller
2. Graphql to handle index and show, can refer to documentation: <https://lighthouse-php.com/6/getting-started/installation.html#install-via-composer>
3. Service
4. Repository, can refer to documentation: <https://github.com/andersao/l5-repository>
5. ControllerTest
6. ServiceTest
7. RepositoryTest
8. Enum should create under App\\Enum: <https://github.com/BenSampo/laravel-enum>

## Naming

| Function Name              | Camel case                     |
| -------------------------- | ------------------------------ |
| Function Variable          | Snake case                     |
| ---                        | ---                            |
| Constant                   | Capital letter with snake case |
| ---                        | ---                            |
| Enum Constant Value        | Kebab case                     |
| ---                        | ---                            |
| Array key                  | Snake case                     |
| ---                        | ---                            |
| Property/instance property | Camel case                     |
| ---                        | ---                            |
| Test case function name    | Normal text                    |
| ---                        | ---                            |

## Injecting Service/Repository

1. Controller

public function \_\_construct(

protected AuthService $authService,

protected UserService $userService,

) {}

1. Service

public function \_\_construct(

protected NotificationService $notificationService,

protected UserProfileRepository $userProfileRepository

)

{

}

## Test Case

1. We are use Pest for the test case, can read the documentation in here: <https://pestphp.com/docs/installation>
2. Every function exists in the controller/service/repository; it must have a test case to handle it.
3. Each controller function needs to test the validation.
4. If $this->relationship is declared in service, it need to add the following test case also

test('check relationship', function () {

$branch = Branch::_factory_()->create();

BranchDetails::_factory_()->create(\[

'branch_id' => $branch->id,

\]);

$branch_model = $this->branchService->setModel($branch)->getModel();

expect($branch_model->toArray())->toHaveKey('details');

});

1. Every service test case should included:

test('check repository', function () {

expect($this->branchService->getRepository())->toBeInstanceOf(BranchRepository::class);

});

1. Every repository test case should included:

test('model', function () {

$response = $this->branchRepository->model();

expect($response)->toEqual(Branch::class);

});

1. Try use the built in core test function instead of write yourself, for more information can refer to this file (**tests/Pest.php)**
2. Sample test controller route unauthenticated:

test('unauthenticated and unauthorized user cannot access user endpoints', function () {

$user = User::_factory_()->create();

// Test all endpoints without authentication

$endpoints = \[

\['POST', route("{$this->routeNamePrefix}.store")\],

\['PUT', route("{$this->routeNamePrefix}.update", \['user' => $user->id\])\],

\['DELETE', route("{$this->routeNamePrefix}.destroy", \['user' => $user->id\])\],

\];

foreach ($endpoints as \[$method, $url\]) {

$response = $this->json($method, $url)->json();

expect($response)->toHaveUnauthenticatedResponse();

}

// Test all endpoints without authorized

Sanctum::_actingAs_($this->unauthorizedUser);

foreach ($endpoints as \[$method, $url\]) {

$response = $this->json($method, $url)->json();

expect($response)->toHaveUnauthorizedPermissionResponse();

}

});

## Controller

1. Main objective: Do the validation, call the service.
2. Initial code and sample how to use it:

public function \_\_construct(

protected UserService $userService,

) {}

public function store(CreateUserRequest $request): JsonResponse

{

$input = $request->validated();

$user = $this->userService

\->store($input)

\->syncProfile($input\['profile'\] ?? \[\])

\->syncRoles($input\['roles'\] ?? \[\])

\->syncBranches($input\['branches'\] ?? \[\])

\->getModel();

}

1. Do not handle the business logic here
2. Response with resource **ApiResponse** as sample below (**app/Http/Resources/ApiResponse.php**):

//with data

return (new ApiResponse())

\->setMessage(\_\_('api.common.success'))

\->setCode(200)

\->setData($user)

\->getResponse();

//without data

return (new ApiResponse())

\->setMessage(\_\_('api.common.success'))

\->getResponse();

## Service

1. Main objective: Do business logic
2. Initial code:

public function \_\_construct(

UserRepository $user_repository,

)

{

$this->repository = $user_repository;

}

1. One service function should do one feature only, break to multiple function if the feature is too complicated
2. Use return $this; if you want the function able to be chain

public function syncProfile(array $profile_data): static

{

$this->userProfileRepository->updateOrCreate(

\['customer_id' => $this->model->id\],

$profile_data

);

return $this;

}

.. ..

public function store(CreateUserRequest $request): JsonResponse

{

$input = $request->validated();

$user = $this->userService

\->store($input)

\->syncProfile($input\['profile'\] ?? \[\])

\->syncRoles($input\['roles'\] ?? \[\])

\->syncBranches($input\['branches'\] ?? \[\])

\->getModel();

}

1. If you want the model to include the relationship, you can set with:

public function \_\_construct()

{

$this->relationship = \['profile', 'branches'\];

}

1. Try to use the built in function in **app/Services/BaseService.php**

## Repository

1. Main objective: Feature that connect to database
2. Initial code:

public function model(): string

{

return User::class;

}
